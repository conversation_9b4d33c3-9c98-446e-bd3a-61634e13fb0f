/* CABLYS Professional Theme Styles */

:root {
  --primary-color: #1976d2;
  --primary-dark: #0d47a1;
  --primary-light: #42a5f5;
  --secondary-color: #dc004e;
  --secondary-dark: #9a0036;
  --secondary-light: #ff5c8d;
  --success-color: #2e7d32;
  --warning-color: #ed6c02;
  --error-color: #d32f2f;
  --background-color: #f5f5f5;
  --card-background: #ffffff;
  --text-primary: #212121;
  --text-secondary: #757575;
  --border-color: #e0e0e0;
}

/* General styles */
body {
  font-family: 'Roboto', sans-serif;
  background-color: var(--background-color);
  color: var(--text-primary);
  margin: 0;
  padding: 0;
}

/* Header and navbar styles */
.cablys-navbar {
  background-color: var(--card-background) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  border-bottom: 1px solid var(--border-color);
}

.cablys-navbar .MuiButton-root {
  font-weight: 500;
  text-transform: none;
  padding: 6px 16px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.cablys-navbar .active-button {
  background-color: rgba(25, 118, 210, 0.08);
  color: var(--primary-color);
}

.cablys-navbar .MuiButton-root:hover {
  background-color: rgba(25, 118, 210, 0.04);
}

.cablys-logo-container {
  display: flex;
  align-items: center;
  margin-right: 16px;
}

.cablys-logo-text {
  font-weight: 700;
  font-size: 1.2rem;
  margin-left: 8px;
  color: var(--primary-color);
  letter-spacing: 0.5px;
}

/* Card styles */
.cablys-card {
  background-color: var(--card-background);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.3s ease;
  overflow: hidden;
}

.cablys-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.cablys-card-header {
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color);
  background-color: rgba(25, 118, 210, 0.04);
}

.cablys-card-content {
  padding: 24px;
}

/* Button styles */
.cablys-button-primary {
  background-color: var(--primary-color) !important;
  color: white !important;
  text-transform: none !important;
  font-weight: 500 !important;
  padding: 8px 22px !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease !important;
}

.cablys-button-primary:hover {
  background-color: var(--primary-dark) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
}

.cablys-button-secondary {
  background-color: white !important;
  color: var(--primary-color) !important;
  border: 1px solid var(--primary-color) !important;
  text-transform: none !important;
  font-weight: 500 !important;
  padding: 8px 22px !important;
  border-radius: 4px !important;
  transition: all 0.2s ease !important;
}

.cablys-button-secondary:hover {
  background-color: rgba(25, 118, 210, 0.04) !important;
}

/* Table styles */
.cablys-table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.cablys-table th {
  background-color: rgba(25, 118, 210, 0.08);
  color: var(--text-primary);
  font-weight: 500;
  text-align: left;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
}

.cablys-table td {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
}

.cablys-table tr:last-child td {
  border-bottom: none;
}

.cablys-table tr:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

/* Form styles */
.cablys-form-control {
  margin-bottom: 24px !important;
}

.cablys-form-control label {
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--text-primary);
}

.cablys-form-control .MuiOutlinedInput-root {
  border-radius: 4px;
}

/* Dashboard styles */
.cablys-dashboard-container {
  padding: 24px;
}

.cablys-dashboard-title {
  font-size: 1.5rem;
  font-weight: 500;
  margin-bottom: 24px;
  color: var(--text-primary);
}

.cablys-dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

/* Footer styles */
.cablys-footer {
  background-color: var(--card-background);
  border-top: 1px solid var(--border-color);
  padding: 16px 24px;
  text-align: center;
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-top: auto;
}