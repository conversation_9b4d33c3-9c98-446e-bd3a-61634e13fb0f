import React, { useState, useEffect } from 'react';
import { Box, Typography, Chip, TableRow, TableCell } from '@mui/material';
import FilterableTable from '../common/FilterableTable';
import { formatDate } from '../../utils/dateUtils';

/**
 * Componente per visualizzare la lista dei cavi con filtri in stile Excel
 *
 * @param {Object} props - Proprietà del componente
 * @param {Array} props.cavi - Lista dei cavi da visualizzare
 * @param {boolean} props.loading - Indica se i dati sono in caricamento
 * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano
 * @param {string} props.revisioneCorrente - Revisione corrente da mostrare nelle statistiche
 */
const CaviFilterableTable = ({ cavi = [], loading = false, onFilteredDataChange = null, revisioneCorrente = null }) => {
  const [filteredCavi, setFilteredCavi] = useState(cavi);

  // Aggiorna i dati filtrati quando cambiano i cavi
  useEffect(() => {
    setFilteredCavi(cavi);
  }, [cavi]);

  // Notifica il componente padre quando cambiano i dati filtrati
  const handleFilteredDataChange = (data) => {
    setFilteredCavi(data);
    if (onFilteredDataChange) {
      onFilteredDataChange(data);
    }
  };



  // Definizione delle colonne
  const columns = [
    {
      field: 'id_cavo',
      headerName: 'ID Cavo',
      dataType: 'text',
      headerStyle: { fontWeight: 'bold' }
    },
    // Colonna Revisione rimossa e spostata nella tabella delle statistiche
    {
      field: 'sistema',
      headerName: 'Sistema',
      dataType: 'text'
    },
    {
      field: 'utility',
      headerName: 'Utility',
      dataType: 'text'
    },
    {
      field: 'tipologia',
      headerName: 'Tipologia',
      dataType: 'text'
    },
    // n_conduttori field is now a spare field (kept in DB but hidden in UI)
    {
      field: 'sezione',
      headerName: 'Formazione',
      dataType: 'text',
      align: 'right',
      cellStyle: { textAlign: 'right' }
    },
    {
      field: 'metri_teorici',
      headerName: 'Metri Teorici',
      dataType: 'number',
      align: 'right',
      cellStyle: { textAlign: 'right' },
      renderCell: (row) => row.metri_teorici ? row.metri_teorici.toFixed(1) : '0'
    },
    {
      field: 'metratura_reale',
      headerName: 'Metri Reali',
      dataType: 'number',
      align: 'right',
      cellStyle: { textAlign: 'right' },
      renderCell: (row) => row.metratura_reale ? row.metratura_reale.toFixed(1) : '0'
    },
    {
      field: 'stato_installazione',
      headerName: 'Stato',
      dataType: 'text',
      renderCell: (row) => {
        let color = 'default';
        if (row.stato_installazione === 'INSTALLATO') color = 'success';
        else if (row.stato_installazione === 'IN_CORSO') color = 'warning';
        else if (row.stato_installazione === 'DA_INSTALLARE') color = 'error';

        return (
          <Chip
            label={row.stato_installazione || 'N/D'}
            size="small"
            color={color}
            variant="outlined"
          />
        );
      }
    },
    {
      field: 'id_bobina',
      headerName: 'Bobina',
      dataType: 'text',
      renderCell: (row) => {
        // Gestione differenziata per null e BOBINA_VUOTA
        if (row.id_bobina === null) {
          // Per cavi non posati (id_bobina è null)
          return '-';
        } else if (row.id_bobina === 'BOBINA_VUOTA') {
          // Per cavi posati senza bobina specifica
          return 'BOBINA VUOTA';
        } else if (!row.id_bobina) {
          // Per altri casi in cui id_bobina è falsy (undefined, stringa vuota)
          return '-';
        }

        // Estrai solo il numero della bobina (parte dopo '_B')
        const match = row.id_bobina.match(/_B(.+)$/);
        return match ? match[1] : row.id_bobina;
      }
    },
    {
      field: 'timestamp',
      headerName: 'Data Modifica',
      dataType: 'date',
      renderCell: (row) => formatDate(row.timestamp)
    },
    {
      field: 'collegamenti',
      headerName: 'Collegamenti',
      dataType: 'number',
      align: 'center',
      cellStyle: { textAlign: 'center' },
      renderCell: (row) => {
        let color = 'default';
        if (row.collegamenti === 2) color = 'success';
        else if (row.collegamenti === 1) color = 'warning';
        else color = 'error';

        return (
          <Chip
            label={row.collegamenti}
            size="small"
            color={color}
            variant="outlined"
          />
        );
      }
    }
  ];

  // Renderizza una riga personalizzata
  const renderRow = (row, index) => {
    // Determina il colore di sfondo in base allo stato
    let bgColor = 'inherit';
    if (row.stato_installazione === 'INSTALLATO') bgColor = 'rgba(76, 175, 80, 0.1)';
    else if (row.stato_installazione === 'IN_CORSO') bgColor = 'rgba(255, 152, 0, 0.1)';

    return (
      <TableRow
        key={index}
        sx={{
          backgroundColor: bgColor,
          '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }
        }}
      >
        {columns.map((column) => (
          <TableCell
            key={column.field}
            align={column.align || 'left'}
            sx={column.cellStyle}
          >
            {column.renderCell ? column.renderCell(row) : row[column.field]}
          </TableCell>
        ))}
      </TableRow>
    );
  };

  // Calcola le statistiche
  const calculateStats = () => {
    // Mostra sempre le statistiche, anche se non ci sono dati
    const totalCavi = filteredCavi.length;
    const installati = filteredCavi.filter(c => c.stato_installazione === 'INSTALLATO').length;
    const inCorso = filteredCavi.filter(c => c.stato_installazione === 'IN_CORSO').length;
    const daInstallare = filteredCavi.filter(c => c.stato_installazione === 'DA_INSTALLARE').length;

    // Calcoli più dettagliati per il progress
    const metriTeoriciTotali = filteredCavi.reduce((sum, c) => sum + (c.metri_teorici || 0), 0);
    const metriInstallati = filteredCavi
      .filter(c => c.stato_installazione === 'INSTALLATO')
      .reduce((sum, c) => sum + (c.metratura_reale || c.metri_teorici || 0), 0);
    const metriRimanenti = metriTeoriciTotali - metriInstallati;

    const percentualeCompletamento = totalCavi ? Math.round((installati / totalCavi) * 100) : 0;
    const percentualeMetri = metriTeoriciTotali ? Math.round((metriInstallati / metriTeoriciTotali) * 100) : 0;

    return {
      totalCavi,
      installati,
      inCorso,
      daInstallare,
      metriTeoriciTotali,
      metriInstallati,
      metriRimanenti,
      percentualeCompletamento,
      percentualeMetri
    };
  };

  const stats = calculateStats();

  return (
    <Box>
      {/* Statistiche sempre visibili */}
      <Box sx={{ mb: 3, p: 3, bgcolor: 'background.paper', borderRadius: 2, boxShadow: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Avanzamento Lavori{revisioneCorrente ? (
              <> <span style={{ fontWeight: 'normal', color: '#666' }}> - Rev. "{revisioneCorrente}"</span></>
            ) : ''}
          </Typography>
        </Box>
        <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: 3 }}>
          <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'primary.light', borderRadius: 1, color: 'white' }}>
            <Typography variant="body2" sx={{ opacity: 0.9 }}>Completamento Cavi</Typography>
            <Typography variant="h5" sx={{ fontWeight: 700 }}>{stats.percentualeCompletamento}%</Typography>
          </Box>
          <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'info.light', borderRadius: 1, color: 'white' }}>
            <Typography variant="body2" sx={{ opacity: 0.9 }}>Completamento Metri</Typography>
            <Typography variant="h5" sx={{ fontWeight: 700 }}>{stats.percentualeMetri}%</Typography>
          </Box>
          <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'success.light', borderRadius: 1, color: 'white' }}>
            <Typography variant="body2" sx={{ opacity: 0.9 }}>Cavi Installati</Typography>
            <Typography variant="h5" sx={{ fontWeight: 700 }}>{stats.installati}</Typography>
          </Box>
          <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'warning.light', borderRadius: 1, color: 'white' }}>
            <Typography variant="body2" sx={{ opacity: 0.9 }}>Cavi in Corso</Typography>
            <Typography variant="h5" sx={{ fontWeight: 700 }}>{stats.inCorso}</Typography>
          </Box>
          <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'error.light', borderRadius: 1, color: 'white' }}>
            <Typography variant="body2" sx={{ opacity: 0.9 }}>Da Installare</Typography>
            <Typography variant="h5" sx={{ fontWeight: 700 }}>{stats.daInstallare}</Typography>
          </Box>
          <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'grey.600', borderRadius: 1, color: 'white' }}>
            <Typography variant="body2" sx={{ opacity: 0.9 }}>Metri Installati</Typography>
            <Typography variant="h5" sx={{ fontWeight: 700 }}>{stats.metriInstallati.toFixed(0)} m</Typography>
          </Box>
          <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'grey.400', borderRadius: 1, color: 'white' }}>
            <Typography variant="body2" sx={{ opacity: 0.9 }}>Metri Rimanenti</Typography>
            <Typography variant="h5" sx={{ fontWeight: 700 }}>{stats.metriRimanenti.toFixed(0)} m</Typography>
          </Box>
        </Box>
      </Box>

      <FilterableTable
        data={cavi}
        columns={columns}
        onFilteredDataChange={handleFilteredDataChange}
        loading={loading}
        emptyMessage="Nessun cavo disponibile"
        renderRow={renderRow}
      />
    </Box>
  );
};

export default CaviFilterableTable;
