from typing import Any, List, Optional
from datetime import datetime, date

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import text

from backend.database import get_db
from backend.models.user import User
from backend.models.cantiere import Cantiere
from backend.models.cavo import Cavo
from backend.models.parco_cavi import ParcoCavo
from backend.schemas.parco_cavi import ParcoCavoCreate, ParcoCavoInDB, ParcoCavoUpdate, StoricoUtilizzoBobina, BobinaCompatibile
from backend.core.security import get_current_active_user
from backend.utils.cable_normalizer import normalize_all_cable_fields

router = APIRouter()

@router.get("/{cantiere_id}", response_model=List[ParcoCavoInDB])
def get_bobine(
    cantiere_id: int,
    filtro: Optional[str] = Query(None, description="Filtra per ID, utility o tipologia"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Ottiene la lista delle bobine di un cantiere.

    Args:
        cantiere_id: ID del cantiere
        filtro: Filtro opzionale per ID, utility o tipologia
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        List[ParcoCavoInDB]: Lista delle bobine del cantiere

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari o se il cantiere non esiste
    """
    # Verifica che il cantiere esista
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Verifica che l'utente sia il proprietario del cantiere o un amministratore
    if cantiere.id_utente != current_user.id_utente and current_user.ruolo != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai i permessi per accedere a questo cantiere"
        )

    # Costruisci la query base
    query = db.query(ParcoCavo).filter(ParcoCavo.id_cantiere == cantiere_id)

    # Aggiungi filtri se necessario
    if filtro:
        query = query.filter(
            (ParcoCavo.id_bobina.ilike(f"%{filtro}%")) |
            (ParcoCavo.utility.ilike(f"%{filtro}%")) |
            (ParcoCavo.tipologia.ilike(f"%{filtro}%"))
        )

    # Ordina per numero_bobina
    query = query.order_by(ParcoCavo.numero_bobina)

    # Esegui la query
    bobine = query.all()

    return bobine

@router.post("/{cantiere_id}", response_model=ParcoCavoInDB)
def create_bobina(
    cantiere_id: int,
    bobina_in: ParcoCavoCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Crea una nuova bobina nel parco cavi di un cantiere.
    Implementa la stessa logica della funzione crea_bobina nella CLI.

    Args:
        cantiere_id: ID del cantiere
        bobina_in: Dati della nuova bobina
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        ParcoCavoInDB: Bobina creata

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari o se il cantiere non esiste
    """
    # Verifica che il cantiere esista
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Verifica che l'utente sia il proprietario del cantiere o un amministratore
    if cantiere.id_utente != current_user.id_utente and current_user.ruolo != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai i permessi per accedere a questo cantiere"
        )

    # Ottieni la configurazione per la generazione degli ID bobina
    # Nella CLI questo è gestito da _get_bobina_config
    existing_bobine = db.query(ParcoCavo).filter(ParcoCavo.id_cantiere == cantiere_id).all()
    is_first_insert = len(existing_bobine) == 0

    # Imposta i valori predefiniti
    config_value = 's'  # Valore predefinito come nella CLI
    usa_numero_progressivo = True  # Valore predefinito come nella CLI

    if is_first_insert:
        # Per il primo inserimento, nella CLI si chiede all'utente
        # Nella webapp, usiamo il valore fornito dal frontend
        if hasattr(bobina_in, 'configurazione') and bobina_in.configurazione is not None:
            config_value = bobina_in.configurazione
            usa_numero_progressivo = config_value == 's'
    else:
        # Per gli inserimenti successivi, recupera la configurazione dalla prima bobina
        for bobina in existing_bobine:
            if bobina.configurazione is not None:
                config_value = bobina.configurazione
                usa_numero_progressivo = config_value == 's'
                break

    # Genera un ID univoco per la bobina (formato: C{id_cantiere}_B{numero_bobina})
    # Nella CLI questo è gestito da _genera_e_verifica_id_bobina e genera_id_bobina
    if usa_numero_progressivo:
        # Trova l'ultimo numero di bobina per questo cantiere
        # Questo è equivalente alla funzione get_ultimo_numero_bobina nella CLI
        ultimo_numero = 0

        # Esegui una query per trovare il massimo numero_bobina
        try:
            # Prima prova a trovare bobine con numero_bobina numerico
            result = db.execute(
                text("""SELECT MAX(CAST(numero_bobina AS INTEGER))
                        FROM parco_cavi
                        WHERE id_cantiere = :cantiere_id
                        AND numero_bobina ~ '^[0-9]+$'"""),
                {"cantiere_id": cantiere_id}
            ).scalar()

            if result is not None:
                ultimo_numero = int(result)
                print(f"Trovato ultimo numero bobina: {ultimo_numero}")
            else:
                print("Nessun numero bobina numerico trovato, usando 0 come default")
        except Exception as e:
            print(f"Errore durante la ricerca del massimo numero_bobina: {str(e)}")
            # In caso di errore, mantieni il valore di default

        # Genera il nuovo numero di bobina
        nuovo_numero = ultimo_numero + 1
        numero_bobina = str(nuovo_numero)
        id_bobina = f"C{cantiere_id}_B{numero_bobina}"
    else:
        # Se non usiamo numeri progressivi, usa l'ID fornito dall'utente
        # Verifica che l'ID sia stato fornito
        if not bobina_in.numero_bobina:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="L'ID della bobina è obbligatorio quando si usa l'inserimento manuale"
            )

        # Usa l'input dell'utente direttamente
        id_input = bobina_in.numero_bobina.strip()

        # Validazione aggiuntiva per l'ID bobina manuale
        if not id_input:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="L'ID della bobina non può essere vuoto"
            )

        # Verifica che l'ID non contenga caratteri speciali non consentiti
        import re
        if re.search(r'[\s\\/:*?"<>|]', id_input):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="L'ID della bobina non può contenere spazi o caratteri speciali come \\ / : * ? \" < > |"
            )

        numero_bobina = id_input  # Salva l'input dell'utente come numero_bobina
        id_bobina = f"C{cantiere_id}_B{id_input}"

    # Verifica che non esista già una bobina con lo stesso ID
    # Questo è equivalente alla funzione verifica_id_bobina_esistente nella CLI
    existing_bobina = db.query(ParcoCavo).filter(ParcoCavo.id_bobina == id_bobina).first()
    if existing_bobina:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Bobina con numero {numero_bobina} già presente nel cantiere"
        )

    # Validazione dei campi obbligatori
    # Nella CLI questo è gestito da _collect_bobina_data
    if not bobina_in.utility:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="L'utility è obbligatoria"
        )

    if not bobina_in.tipologia:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="La tipologia è obbligatoria"
        )

    # Gestione n_conduttori: accetta 0 o stringa vuota
    if bobina_in.n_conduttori is None:
        bobina_in.n_conduttori = "0"

    # Gestione sezione: accetta 0 o stringa vuota
    if bobina_in.sezione is None:
        bobina_in.sezione = "0"

    # Validazione dei metri totali
    if bobina_in.metri_totali <= 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="I metri totali devono essere maggiori di zero"
        )

    # Prepara i dati della bobina per la normalizzazione
    bobina_data = {
        'utility': bobina_in.utility,
        'tipologia': bobina_in.tipologia,
        'n_conduttori': bobina_in.n_conduttori,
        'sezione': bobina_in.sezione,
        'ubicazione_bobina': bobina_in.ubicazione_bobina or "TBD",
        'fornitore': bobina_in.fornitore or "TBD",
        'n_DDT': bobina_in.n_DDT or "TBD"
    }

    # Applica la normalizzazione dei dati
    normalized_data = normalize_all_cable_fields(bobina_data)

    # Crea la nuova bobina con i dati normalizzati
    # Nella CLI questo è gestito da _insert_bobina_to_db
    db_bobina = ParcoCavo(
        id_bobina=id_bobina,
        numero_bobina=numero_bobina,  # Usa il numero generato o fornito
        utility=normalized_data['utility'],
        tipologia=normalized_data['tipologia'],
        n_conduttori=normalized_data['n_conduttori'],
        sezione=normalized_data['sezione'],
        metri_totali=bobina_in.metri_totali,
        metri_residui=bobina_in.metri_totali,  # Inizialmente i metri residui sono uguali ai metri totali
        stato_bobina="Disponibile",  # Esattamente lo stesso valore di StatoBobina.DISPONIBILE.value
        ubicazione_bobina=normalized_data['ubicazione_bobina'],
        fornitore=normalized_data['fornitore'],
        n_DDT=normalized_data['n_DDT'],
        data_DDT=bobina_in.data_DDT,
        configurazione=config_value,
        id_cantiere=cantiere_id
    )

    # Salva la bobina nel database
    try:
        # Log dei dati prima dell'inserimento
        print(f"Tentativo di inserimento bobina: id_bobina={id_bobina}, numero_bobina={numero_bobina}, configurazione={config_value}")
        print(f"Dati completi: {db_bobina.__dict__}")

        db.add(db_bobina)
        db.commit()
        db.refresh(db_bobina)
        print(f"Bobina inserita con successo: {db_bobina.id_bobina}")
        return db_bobina
    except Exception as e:
        db.rollback()
        print(f"ERRORE durante l'inserimento della bobina: {str(e)}")
        # Fornisci un messaggio di errore più dettagliato
        error_detail = str(e)
        if "unique constraint" in error_detail.lower() or "duplicate key" in error_detail.lower():
            error_detail = f"Bobina con ID {id_bobina} già esistente nel database"
        elif "not null constraint" in error_detail.lower():
            error_detail = "Uno o più campi obbligatori non sono stati forniti"

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante l'inserimento della bobina: {error_detail}"
        )

@router.put("/{cantiere_id}/{bobina_numero}", response_model=ParcoCavoInDB)
def update_bobina(
    cantiere_id: int,
    bobina_numero: str,
    bobina_in: ParcoCavoUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Aggiorna una bobina esistente.
    Implementa la stessa logica della funzione modifica_bobina nella CLI.

    Args:
        cantiere_id: ID del cantiere
        bobina_numero: Numero della bobina (parte Y dell'ID, senza il prefisso C{id_cantiere}_B)
        bobina_in: Dati aggiornati della bobina
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        ParcoCavoInDB: Bobina aggiornata

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari o se la bobina non esiste
    """
    # Verifica che il cantiere esista
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Verifica che l'utente sia il proprietario del cantiere o un amministratore
    if cantiere.id_utente != current_user.id_utente and current_user.ruolo != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai i permessi per accedere a questo cantiere"
        )

    # Costruisci l'ID completo della bobina (formato: C{id_cantiere}_B{bobina_numero})
    id_bobina = f"C{cantiere_id}_B{bobina_numero}"

    # Verifica che la bobina esista
    db_bobina = db.query(ParcoCavo).filter(
        ParcoCavo.id_bobina == id_bobina,
        ParcoCavo.id_cantiere == cantiere_id
    ).first()

    if not db_bobina:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Bobina con numero {bobina_numero} non trovata nel cantiere"
        )

    # Verifica che la bobina sia disponibile
    if db_bobina.stato_bobina != "Disponibile":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Impossibile modificare la bobina {bobina_numero}: non è disponibile (stato: {db_bobina.stato_bobina})"
        )

    # Prepara gli aggiornamenti
    update_data = bobina_in.dict(exclude_unset=True)

    # Applica la normalizzazione ai campi di testo se presenti
    if update_data:
        normalized_data = normalize_all_cable_fields(update_data)
        # Sostituisci i valori originali con quelli normalizzati
        for field in ['utility', 'tipologia', 'n_conduttori', 'sezione', 'ubicazione_bobina', 'fornitore', 'n_DDT']:
            if field in update_data and field in normalized_data:
                update_data[field] = normalized_data[field]

    # Verifica se ci sono modifiche
    updates = {}
    for field, value in update_data.items():
        if value is not None and getattr(db_bobina, field) != value:
            updates[field] = value

    # Se non ci sono modifiche, restituisci la bobina senza aggiornamenti
    if not updates:
        return db_bobina

    # Gestione speciale per i metri totali
    # Nella CLI, i metri totali possono essere modificati solo se i metri residui sono uguali ai metri totali
    if 'metri_totali' in updates and db_bobina.metri_residui != db_bobina.metri_totali:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Impossibile modificare i metri totali: la bobina è già stata utilizzata"
        )

    # Se i metri totali vengono modificati, aggiorna anche i metri residui
    if 'metri_totali' in updates and db_bobina.metri_residui == db_bobina.metri_totali:
        updates['metri_residui'] = updates['metri_totali']

    # Aggiorna i campi della bobina
    for field, value in updates.items():
        setattr(db_bobina, field, value)

    # Salva le modifiche nel database
    db.commit()
    db.refresh(db_bobina)

    return db_bobina

@router.delete("/{cantiere_id}/{bobina_numero}", response_model=dict)
def delete_bobina(
    cantiere_id: int,
    bobina_numero: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Elimina una bobina.
    Implementa la stessa logica della funzione elimina_bobina nella CLI.
    Verifica anche se è l'ultima bobina nel cantiere per gestire il reset del parco cavi.

    Args:
        cantiere_id: ID del cantiere
        bobina_numero: Numero della bobina (parte Y dell'ID, senza il prefisso C{id_cantiere}_B)
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        dict: Messaggio di conferma e flag is_last_bobina

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari o se la bobina non esiste
    """
    # Verifica che il cantiere esista
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Verifica che l'utente sia il proprietario del cantiere o un amministratore
    if cantiere.id_utente != current_user.id_utente and current_user.ruolo != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai i permessi per accedere a questo cantiere"
        )

    # Costruisci l'ID completo della bobina (formato: C{id_cantiere}_B{bobina_numero})
    id_bobina = f"C{cantiere_id}_B{bobina_numero}"

    # Verifica che la bobina esista
    db_bobina = db.query(ParcoCavo).filter(
        ParcoCavo.id_bobina == id_bobina,
        ParcoCavo.id_cantiere == cantiere_id
    ).first()

    if not db_bobina:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Bobina con numero {bobina_numero} non trovata nel cantiere"
        )

    # Verifica se la bobina è utilizzata da qualche cavo
    cavi_con_bobina = db.query(Cavo).filter(Cavo.id_bobina == id_bobina).count()
    if cavi_con_bobina > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Impossibile eliminare la bobina: è utilizzata da {cavi_con_bobina} cavi"
        )

    # Verifica che la bobina sia completamente integra (metri_residui = metri_totali)
    if db_bobina.metri_residui != db_bobina.metri_totali:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Impossibile eliminare la bobina: è stata già utilizzata (metri residui: {db_bobina.metri_residui}, metri totali: {db_bobina.metri_totali})"
        )

    # Verifica che la bobina sia nello stato "Disponibile"
    if db_bobina.stato_bobina != "Disponibile":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Impossibile eliminare la bobina: non è nello stato 'Disponibile' (stato attuale: {db_bobina.stato_bobina})"
        )

    # Conta il numero totale di bobine nel cantiere per verificare se è l'ultima
    total_bobine = db.query(ParcoCavo).filter(ParcoCavo.id_cantiere == cantiere_id).count()
    is_last_bobina = total_bobine == 1

    # Elimina la bobina
    db.delete(db_bobina)
    db.commit()

    # Log se è l'ultima bobina
    if is_last_bobina:
        print(f"Eliminata l'ultima bobina del cantiere {cantiere_id}. Il parco cavi è ora vuoto.")

    return {
        "message": f"Bobina {bobina_numero} eliminata con successo",
        "is_last_bobina": is_last_bobina
    }

@router.get("/{cantiere_id}/is-first-insertion", response_model=dict)
def check_first_insertion(
    cantiere_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Verifica se è il primo inserimento di una bobina per un cantiere e restituisce la configurazione esistente.
    Utilizza un approccio semplificato: se esiste una bobina con numero_bobina >= 1, non è il primo inserimento.

    Args:
        cantiere_id: ID del cantiere
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        dict: Dizionario con il flag is_first_insertion e la configurazione esistente
    """
    # Verifica che il cantiere esista
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Verifica che l'utente sia il proprietario del cantiere o un amministratore
    if cantiere.id_utente != current_user.id_utente and current_user.ruolo != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai i permessi per accedere a questo cantiere"
        )

    # Verifica se ci sono già bobine per questo cantiere
    # Approccio semplificato: se esiste almeno una bobina, non è il primo inserimento
    existing_bobina = db.query(ParcoCavo).filter(ParcoCavo.id_cantiere == cantiere_id).first()
    is_first_insertion = existing_bobina is None

    # Se non è il primo inserimento, recupera la configurazione dalla bobina esistente
    configurazione = 's'  # Valore predefinito
    if not is_first_insertion and existing_bobina.configurazione is not None:
        configurazione = existing_bobina.configurazione

    return {
        "is_first_insertion": is_first_insertion,
        "configurazione": configurazione
    }

@router.get("/{cantiere_id}/storico", response_model=List[StoricoUtilizzoBobina])
def get_storico_utilizzo(
    cantiere_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Ottiene lo storico di utilizzo delle bobine di un cantiere.
    Implementa la stessa logica della funzione visualizza_utilizzo_bobine nella CLI.

    Args:
        cantiere_id: ID del cantiere
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        List[StoricoUtilizzoBobina]: Lista dello storico di utilizzo delle bobine

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari o se il cantiere non esiste
    """
    # Verifica che il cantiere esista
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Verifica che l'utente sia il proprietario del cantiere o un amministratore
    if cantiere.id_utente != current_user.id_utente and current_user.ruolo != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai i permessi per accedere a questo cantiere"
        )

    # Ottieni tutte le bobine del cantiere
    bobine = db.query(ParcoCavo).filter(ParcoCavo.id_cantiere == cantiere_id).all()

    # Prepara il risultato
    result = []
    for bobina in bobine:
        # Ottieni i cavi che utilizzano questa bobina
        cavi = db.query(Cavo).filter(
            Cavo.id_bobina == bobina.id_bobina,
            Cavo.id_cantiere == cantiere_id
        ).all()

        # Calcola i metri utilizzati totali
        metri_utilizzati = 0
        for cavo in cavi:
            if cavo.metratura_reale:
                metri_utilizzati += cavo.metratura_reale

        # Crea l'oggetto StoricoUtilizzoBobina
        storico = StoricoUtilizzoBobina(
            id_bobina=bobina.id_bobina,
            numero_bobina=bobina.numero_bobina,
            utility=bobina.utility,
            tipologia=bobina.tipologia,
            n_conduttori=bobina.n_conduttori,
            sezione=bobina.sezione,
            metri_totali=bobina.metri_totali,
            metri_residui=bobina.metri_residui,
            stato_bobina=bobina.stato_bobina,
            cavi=[{
                "id_cavo": cavo.id_cavo,
                "tipologia": cavo.tipologia,
                "n_conduttori": cavo.n_conduttori,
                "sezione": cavo.sezione,
                "metri_teorici": cavo.metri_teorici,
                "metratura_reale": cavo.metratura_reale,
                "stato_installazione": cavo.stato_installazione,
                "timestamp": cavo.timestamp
            } for cavo in cavi]
        )
        result.append(storico)

    # Ordina il risultato per numero_bobina
    result.sort(key=lambda x: x.numero_bobina if x.numero_bobina else "")

    return result

@router.get("/{cantiere_id}/compatibili", response_model=List[BobinaCompatibile])
def get_bobine_compatibili(
    cantiere_id: int,
    tipologia: Optional[str] = Query(None, description="Tipologia del cavo"),
    n_conduttori: Optional[str] = Query(None, description="Numero di conduttori del cavo"),
    sezione: Optional[str] = Query(None, description="Sezione del cavo"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Ottiene la lista delle bobine compatibili con un cavo specifico.
    Implementa la stessa logica della funzione get_bobine_disponibili nella CLI.

    Args:
        cantiere_id: ID del cantiere
        tipologia: Tipologia del cavo
        n_conduttori: Numero di conduttori del cavo
        sezione: Sezione del cavo
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        List[BobinaCompatibile]: Lista delle bobine compatibili
    """
    print("\n==== RICERCA BOBINE COMPATIBILI ====")
    print(f"Parametri ricevuti: cantiere_id={cantiere_id}, tipologia={tipologia}, n_conduttori={n_conduttori}, sezione={sezione}")
    print(f"Tipi dei parametri: tipologia={type(tipologia)}, n_conduttori={type(n_conduttori)}, sezione={type(sezione)}")

    # Verifica che il cantiere esista
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Verifica che l'utente sia il proprietario del cantiere o un amministratore
    if cantiere.id_utente != current_user.id_utente and current_user.ruolo != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai i permessi per accedere a questo cantiere"
        )

    # Prepara i parametri di ricerca
    # Converti esplicitamente in stringhe e gestisci valori null o vuoti
    tipologia_str = str(tipologia or '')

    # Gestione speciale per n_conduttori che potrebbe essere nel formato "X x Y"
    n_conduttori_input = str(n_conduttori or '0')
    # Se il formato è "X x Y", estrai solo il primo numero
    if ' x ' in n_conduttori_input:
        n_conduttori_parts = n_conduttori_input.split(' x ')
        n_conduttori_str = n_conduttori_parts[0]
        print(f"Formato n_conduttori 'X x Y' rilevato: {n_conduttori_input} -> {n_conduttori_str}")
    else:
        n_conduttori_str = n_conduttori_input

    sezione_str = str(sezione or '0')

    print(f"Parametri dopo conversione: tipologia='{tipologia_str}', n_conduttori='{n_conduttori_str}', sezione='{sezione_str}'")

    # Usa parametri con bind per evitare problemi di SQL injection e garantire il corretto tipo
    try:
        # Costruisci la query usando parametri bind per sicurezza
        # Nota: n_conduttori non è più utilizzato per la compatibilità
        # Modifica temporanea: usa UPPER e TRIM per normalizzare i valori
        query = """
        SELECT id_bobina, numero_bobina, tipologia, n_conduttori, sezione, metri_residui, stato_bobina
        FROM parco_cavi
        WHERE id_cantiere = :cantiere_id
          AND UPPER(TRIM(tipologia)) = UPPER(TRIM(:tipologia))
          AND UPPER(TRIM(sezione)) = UPPER(TRIM(:sezione))
          AND stato_bobina NOT IN ('Over', 'Terminata')
          AND metri_residui > 0
        ORDER BY
          CASE WHEN stato_bobina = 'Disponibile' THEN 0 ELSE 1 END,
          metri_residui DESC,
          id_bobina
        """

        # Prepara i parametri per la query
        # Nota: n_conduttori non è più utilizzato per la compatibilità
        params = {
            "cantiere_id": cantiere_id,
            "tipologia": tipologia_str,
            "sezione": sezione_str
        }

        print(f"Query SQL con parametri: {query}")
        print(f"Parametri: {params}")

        # Log di tutte le bobine disponibili per debug
        all_bobine = db.execute(text("""SELECT id_bobina, tipologia, sezione, metri_residui, stato_bobina FROM parco_cavi WHERE id_cantiere = :cantiere_id"""), {"cantiere_id": cantiere_id}).fetchall()
        print(f"\nTutte le bobine disponibili nel cantiere {cantiere_id}:")
        for bobina in all_bobine:
            bobina_dict = dict(bobina._mapping)
            print(f"  - ID: {bobina_dict['id_bobina']}, Tipologia: '{bobina_dict['tipologia']}', Sezione: '{bobina_dict['sezione']}', Metri residui: {bobina_dict['metri_residui']}, Stato: {bobina_dict['stato_bobina']}")

        # Esegui la query
        result = db.execute(text(query), params).fetchall()
        print(f"\nRisultato query: {len(result)} bobine trovate")

        # Converti i risultati in oggetti BobinaCompatibile
        bobine_compatibili = []
        for row in result:
            # Converti la riga in un dizionario
            bobina_dict = dict(row._mapping)
            print(f"Bobina trovata: {bobina_dict}")

            # Crea l'oggetto BobinaCompatibile
            bobina_compatibile = BobinaCompatibile(
                id_bobina=bobina_dict['id_bobina'],
                numero_bobina=bobina_dict['numero_bobina'],
                tipologia=bobina_dict['tipologia'],
                n_conduttori=bobina_dict['n_conduttori'],
                sezione=bobina_dict['sezione'],
                metri_residui=bobina_dict['metri_residui'],
                stato_bobina=bobina_dict['stato_bobina']
            )
            bobine_compatibili.append(bobina_compatibile)

        return bobine_compatibili

    except Exception as e:
        print(f"Errore durante la ricerca delle bobine compatibili: {str(e)}")
        # In caso di errore, prova con l'approccio ORM
        print("Tentativo con approccio ORM...")

        # Costruisci la query ORM
        # Nota: n_conduttori non è più utilizzato per la compatibilità
        query = db.query(ParcoCavo).filter(
            ParcoCavo.id_cantiere == cantiere_id,
            ParcoCavo.tipologia == tipologia_str,
            ParcoCavo.sezione == sezione_str,
            ParcoCavo.stato_bobina.notin_(["Over", "Terminata"]),
            ParcoCavo.metri_residui > 0
        ).order_by(
            text("CASE WHEN stato_bobina = 'Disponibile' THEN 0 ELSE 1 END"),
            ParcoCavo.metri_residui.desc(),
            ParcoCavo.id_bobina
        )

        print(f"Query ORM: {query}")

        # Esegui la query
        bobine = query.all()
        print(f"Risultato query ORM: {len(bobine)} bobine trovate")

        # Converti i risultati in BobinaCompatibile
        result = []
        for bobina in bobine:
            print(f"Bobina: id_bobina={bobina.id_bobina}, tipologia={bobina.tipologia}, n_conduttori={bobina.n_conduttori}, sezione={bobina.sezione}, metri_residui={bobina.metri_residui}, stato_bobina={bobina.stato_bobina}")

            # Crea l'oggetto BobinaCompatibile
            bobina_compatibile = BobinaCompatibile(
                id_bobina=bobina.id_bobina,
                numero_bobina=bobina.numero_bobina,
                tipologia=bobina.tipologia,
                n_conduttori=bobina.n_conduttori,
                sezione=bobina.sezione,
                metri_residui=bobina.metri_residui,
                stato_bobina=bobina.stato_bobina
            )
            result.append(bobina_compatibile)

        return result
