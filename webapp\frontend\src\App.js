import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';

import { useAuth } from './context/AuthContext';
import LoginPage from './pages/LoginPageNew';
import Dashboard from './pages/Dashboard';
import ProtectedRoute from './components/ProtectedRoute';
import './styles/cablys-theme.css';

// Tema personalizzato CABLYS
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
      dark: '#0d47a1',
      light: '#42a5f5',
    },
    secondary: {
      main: '#dc004e',
      dark: '#9a0036',
      light: '#ff5c8d',
    },
    info: {
      main: '#0288d1',
    },
    success: {
      main: '#2e7d32',
    },
    warning: {
      main: '#ed6c02',
    },
    error: {
      main: '#d32f2f',
    },
    background: {
      default: '#f5f5f5',
      paper: '#ffffff',
    },
    text: {
      primary: '#212121',
      secondary: '#757575',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 500,
    },
    h2: {
      fontWeight: 500,
    },
    h3: {
      fontWeight: 500,
    },
    h4: {
      fontWeight: 500,
    },
    h5: {
      fontWeight: 500,
    },
    h6: {
      fontWeight: 500,
    },
    button: {
      textTransform: 'none',
      fontWeight: 500,
    },
  },
  shape: {
    borderRadius: 4,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 4,
          textTransform: 'none',
          fontWeight: 500,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
        },
      },
    },
    MuiCardHeader: {
      styleOverrides: {
        root: {
          padding: '16px 24px',
          borderBottom: '1px solid #e0e0e0',
          backgroundColor: 'rgba(25, 118, 210, 0.04)',
        },
      },
    },
    MuiCardContent: {
      styleOverrides: {
        root: {
          padding: 24,
        },
      },
    },
  },
});

function App() {
  const { isAuthenticated, loading, user } = useAuth();

  console.log('App - Stato autenticazione:', { isAuthenticated, loading });

  // Se l'applicazione è in caricamento, mostra un indicatore di caricamento
  if (loading) {
    console.log('App - Mostrando indicatore di caricamento');
    return (
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
          <div style={{ textAlign: 'center' }}>
            <div>Caricamento...</div>
          </div>
        </div>
      </ThemeProvider>
    );
  }

  console.log('App - Rendering principale, isAuthenticated:', isAuthenticated);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Routes>
          <Route path="/login" element={
          isAuthenticated ? (
            user?.role === 'owner' ? <Navigate to="/dashboard/admin" replace /> :
            user?.role === 'user' ? <Navigate to="/dashboard/cantieri" replace /> :
            user?.role === 'cantieri_user' ? <Navigate to="/dashboard/cavi/visualizza" replace /> :
            <Navigate to="/dashboard" replace />
          ) : <LoginPage />
        } />
        <Route
          path="/dashboard/*"
          element={
            <ProtectedRoute>
              <Dashboard />
            </ProtectedRoute>
          }
        />
        <Route
          path="/"
          element={
            isAuthenticated ? (
              user?.role === 'owner' ? <Navigate to="/dashboard/admin" replace /> :
              user?.role === 'user' ? <Navigate to="/dashboard/cantieri" replace /> :
              user?.role === 'cantieri_user' ? <Navigate to="/dashboard/cavi/visualizza" replace /> :
              <Navigate to="/dashboard" replace />
            ) : (
              <Navigate to="/login" replace />
            )
          }
        />
      </Routes>
    </ThemeProvider>
  );
}

export default App;
