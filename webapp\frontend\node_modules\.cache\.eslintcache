[{"C:\\CMS\\webapp\\frontend\\src\\index.js": "1", "C:\\CMS\\webapp\\frontend\\src\\App.js": "2", "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js": "3", "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js": "4", "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js": "5", "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js": "6", "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js": "7", "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js": "8", "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js": "9", "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js": "10", "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js": "11", "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js": "12", "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js": "13", "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js": "14", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js": "15", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCaviPage.js": "16", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js": "17", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js": "18", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js": "19", "C:\\CMS\\webapp\\frontend\\src\\config.js": "20", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js": "21", "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js": "22", "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx": "23", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js": "24", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js": "25", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js": "26", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js": "27", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\StoricoUtilizzoPage.js": "28", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js": "29", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\MetriPosatiSemplificatoPage.js": "30", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaBobinaPage.js": "31", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaCavoPage.js": "32", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\InserisciMetriPage.js": "33", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\CollegamentiPage.js": "34", "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js": "35", "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js": "36", "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js": "37", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js": "38", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js": "39", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js": "40", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js": "41", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js": "42", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js": "43", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js": "44", "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js": "45", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js": "46", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js": "47", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCavi.js": "48", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js": "49", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js": "50", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\PosaCaviCollegamenti.js": "51", "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js": "52", "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js": "53", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js": "54", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js": "55", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js": "56", "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js": "57", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js": "58", "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js": "59", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js": "60", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js": "61", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx": "62", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx": "63", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx": "64", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx": "65", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js": "66", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js": "67", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js": "68", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaForm.js": "69", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriForm.js": "70", "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js": "71", "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js": "72", "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js": "73", "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js": "74", "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js": "75", "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js": "76", "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js": "77", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js": "78", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js": "79", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js": "80", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js": "81", "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js": "82", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js": "83", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js": "84", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js": "85", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js": "86", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js": "87", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js": "88", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js": "89", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js": "90", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js": "91", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js": "92", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCaviImproved.js": "93", "C:\\CMS\\webapp\\frontend\\src\\pages\\comande\\ComandePage.js": "94", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeList.js": "95", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\TestComande.js": "96", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\AccessoRapidoComanda.js": "97", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCEI64_8Page.js": "98", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\CertificazioneCEI64_8.js": "99", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\RapportiGenerali.js": "100", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\ProveDettagliate.js": "101", "C:\\CMS\\webapp\\frontend\\src\\services\\nonConformitaService.js": "102", "C:\\CMS\\webapp\\frontend\\src\\services\\rapportiGeneraliService.js": "103", "C:\\CMS\\webapp\\frontend\\src\\services\\proveDettagliateService.js": "104"}, {"size": 557, "mtime": 1746952718482, "results": "105", "hashOfConfig": "106"}, {"size": 2728, "mtime": 1746952740200, "results": "107", "hashOfConfig": "106"}, {"size": 996, "mtime": 1746970152489, "results": "108", "hashOfConfig": "106"}, {"size": 10788, "mtime": 1746864244183, "results": "109", "hashOfConfig": "106"}, {"size": 21191, "mtime": 1748751093271, "results": "110", "hashOfConfig": "106"}, {"size": 7101, "mtime": 1748756111287, "results": "111", "hashOfConfig": "106"}, {"size": 2216, "mtime": 1746640055487, "results": "112", "hashOfConfig": "106"}, {"size": 7394, "mtime": 1748034003517, "results": "113", "hashOfConfig": "106"}, {"size": 6749, "mtime": 1746282201800, "results": "114", "hashOfConfig": "106"}, {"size": 18495, "mtime": 1748722926763, "results": "115", "hashOfConfig": "106"}, {"size": 2535, "mtime": 1746647873596, "results": "116", "hashOfConfig": "106"}, {"size": 2050, "mtime": 1746647945415, "results": "117", "hashOfConfig": "106"}, {"size": 700, "mtime": 1747545501078, "results": "118", "hashOfConfig": "106"}, {"size": 17518, "mtime": 1748664526035, "results": "119", "hashOfConfig": "106"}, {"size": 3028, "mtime": 1748816305304, "results": "120", "hashOfConfig": "106"}, {"size": 2070, "mtime": 1748815989656, "results": "121", "hashOfConfig": "106"}, {"size": 1630, "mtime": 1746336079554, "results": "122", "hashOfConfig": "106"}, {"size": 1909, "mtime": 1748722592098, "results": "123", "hashOfConfig": "106"}, {"size": 37705, "mtime": 1748876005635, "results": "124", "hashOfConfig": "106"}, {"size": 324, "mtime": 1748757444974, "results": "125", "hashOfConfig": "106"}, {"size": 9068, "mtime": 1746856425683, "results": "126", "hashOfConfig": "106"}, {"size": 2210, "mtime": 1747432283057, "results": "127", "hashOfConfig": "106"}, {"size": 4494, "mtime": 1748121063631, "results": "128", "hashOfConfig": "106"}, {"size": 38195, "mtime": 1748813903832, "results": "129", "hashOfConfig": "106"}, {"size": 3337, "mtime": 1748816346924, "results": "130", "hashOfConfig": "106"}, {"size": 2958, "mtime": 1748816316425, "results": "131", "hashOfConfig": "106"}, {"size": 3507, "mtime": 1748816326922, "results": "132", "hashOfConfig": "106"}, {"size": 3345, "mtime": 1748816357091, "results": "133", "hashOfConfig": "106"}, {"size": 3340, "mtime": 1748816336281, "results": "134", "hashOfConfig": "106"}, {"size": 2975, "mtime": 1747554796402, "results": "135", "hashOfConfig": "106"}, {"size": 3429, "mtime": 1747721794176, "results": "136", "hashOfConfig": "106"}, {"size": 3109, "mtime": 1747824114392, "results": "137", "hashOfConfig": "106"}, {"size": 2929, "mtime": 1747655572696, "results": "138", "hashOfConfig": "106"}, {"size": 3302, "mtime": 1748000902435, "results": "139", "hashOfConfig": "106"}, {"size": 6125, "mtime": 1748705680231, "results": "140", "hashOfConfig": "106"}, {"size": 5880, "mtime": 1748121404574, "results": "141", "hashOfConfig": "106"}, {"size": 3889, "mtime": 1748664890350, "results": "142", "hashOfConfig": "106"}, {"size": 4720, "mtime": 1746771178920, "results": "143", "hashOfConfig": "106"}, {"size": 7121, "mtime": 1746281148395, "results": "144", "hashOfConfig": "106"}, {"size": 7958, "mtime": 1746280443400, "results": "145", "hashOfConfig": "106"}, {"size": 6259, "mtime": 1746965906057, "results": "146", "hashOfConfig": "106"}, {"size": 4215, "mtime": 1746278746358, "results": "147", "hashOfConfig": "106"}, {"size": 1273, "mtime": 1746809069006, "results": "148", "hashOfConfig": "106"}, {"size": 14270, "mtime": 1748371983481, "results": "149", "hashOfConfig": "106"}, {"size": 2752, "mtime": 1747022186740, "results": "150", "hashOfConfig": "106"}, {"size": 1072, "mtime": 1746637929350, "results": "151", "hashOfConfig": "106"}, {"size": 6745, "mtime": 1747545492454, "results": "152", "hashOfConfig": "106"}, {"size": 41680, "mtime": 1748816669877, "results": "153", "hashOfConfig": "106"}, {"size": 500, "mtime": 1748722841235, "results": "154", "hashOfConfig": "106"}, {"size": 47271, "mtime": 1748072224692, "results": "155", "hashOfConfig": "106"}, {"size": 38669, "mtime": 1748199713253, "results": "156", "hashOfConfig": "106"}, {"size": 1947, "mtime": 1748120984640, "results": "157", "hashOfConfig": "106"}, {"size": 54895, "mtime": 1748370360136, "results": "158", "hashOfConfig": "106"}, {"size": 14635, "mtime": 1748666301849, "results": "159", "hashOfConfig": "106"}, {"size": 9899, "mtime": 1748875949033, "results": "160", "hashOfConfig": "106"}, {"size": 11771, "mtime": 1746948731812, "results": "161", "hashOfConfig": "106"}, {"size": 2211, "mtime": 1748686293878, "results": "162", "hashOfConfig": "106"}, {"size": 9215, "mtime": 1748668814050, "results": "163", "hashOfConfig": "106"}, {"size": 10993, "mtime": 1747154871546, "results": "164", "hashOfConfig": "106"}, {"size": 12150, "mtime": 1748205557322, "results": "165", "hashOfConfig": "106"}, {"size": 24566, "mtime": 1748691444876, "results": "166", "hashOfConfig": "106"}, {"size": 7032, "mtime": 1748069273238, "results": "167", "hashOfConfig": "106"}, {"size": 8589, "mtime": 1748207111023, "results": "168", "hashOfConfig": "106"}, {"size": 9979, "mtime": 1748069243848, "results": "169", "hashOfConfig": "106"}, {"size": 10821, "mtime": 1748069202177, "results": "170", "hashOfConfig": "106"}, {"size": 36555, "mtime": 1747684003188, "results": "171", "hashOfConfig": "106"}, {"size": 9483, "mtime": 1747194869458, "results": "172", "hashOfConfig": "106"}, {"size": 16178, "mtime": 1748875708468, "results": "173", "hashOfConfig": "106"}, {"size": 48588, "mtime": 1747948123233, "results": "174", "hashOfConfig": "106"}, {"size": 92270, "mtime": 1748123070273, "results": "175", "hashOfConfig": "106"}, {"size": 522, "mtime": 1747022186711, "results": "176", "hashOfConfig": "106"}, {"size": 10251, "mtime": 1748805459799, "results": "177", "hashOfConfig": "106"}, {"size": 5994, "mtime": 1748696862956, "results": "178", "hashOfConfig": "106"}, {"size": 1703, "mtime": 1746972529152, "results": "179", "hashOfConfig": "106"}, {"size": 19892, "mtime": 1747554544219, "results": "180", "hashOfConfig": "106"}, {"size": 12050, "mtime": 1747547543421, "results": "181", "hashOfConfig": "106"}, {"size": 1686, "mtime": 1746946499500, "results": "182", "hashOfConfig": "106"}, {"size": 5145, "mtime": 1746914029633, "results": "183", "hashOfConfig": "106"}, {"size": 10721, "mtime": 1748751269815, "results": "184", "hashOfConfig": "106"}, {"size": 22179, "mtime": 1747432554979, "results": "185", "hashOfConfig": "106"}, {"size": 2258, "mtime": 1746946368534, "results": "186", "hashOfConfig": "106"}, {"size": 4094, "mtime": 1748161663641, "results": "187", "hashOfConfig": "106"}, {"size": 5273, "mtime": 1747946737459, "results": "188", "hashOfConfig": "106"}, {"size": 4346, "mtime": 1747491472989, "results": "189", "hashOfConfig": "106"}, {"size": 15571, "mtime": 1747980774491, "results": "190", "hashOfConfig": "106"}, {"size": 6742, "mtime": 1748751174061, "results": "191", "hashOfConfig": "106"}, {"size": 6529, "mtime": 1748664406267, "results": "192", "hashOfConfig": "106"}, {"size": 15739, "mtime": 1748664968476, "results": "193", "hashOfConfig": "106"}, {"size": 6448, "mtime": 1748664917658, "results": "194", "hashOfConfig": "106"}, {"size": 5536, "mtime": 1748670096009, "results": "195", "hashOfConfig": "106"}, {"size": 5457, "mtime": 1748666884369, "results": "196", "hashOfConfig": "106"}, {"size": 5605, "mtime": 1748666925194, "results": "197", "hashOfConfig": "106"}, {"size": 73589, "mtime": 1748816649087, "results": "198", "hashOfConfig": "106"}, {"size": 2807, "mtime": 1748705699971, "results": "199", "hashOfConfig": "106"}, {"size": 22770, "mtime": 1748724489409, "results": "200", "hashOfConfig": "106"}, {"size": 3708, "mtime": 1748705727900, "results": "201", "hashOfConfig": "106"}, {"size": 10270, "mtime": 1748724524628, "results": "202", "hashOfConfig": "106"}, {"size": 8247, "mtime": 1748756088995, "results": "203", "hashOfConfig": "106"}, {"size": 11038, "mtime": 1748756003708, "results": "204", "hashOfConfig": "106"}, {"size": 15055, "mtime": 1748755908778, "results": "205", "hashOfConfig": "106"}, {"size": 16415, "mtime": 1748755956687, "results": "206", "hashOfConfig": "106"}, {"size": 3434, "mtime": 1748755857115, "results": "207", "hashOfConfig": "106"}, {"size": 3483, "mtime": 1748755829302, "results": "208", "hashOfConfig": "106"}, {"size": 3508, "mtime": 1748755842942, "results": "209", "hashOfConfig": "106"}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1f0jzw9", {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\CMS\\webapp\\frontend\\src\\index.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\App.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js", ["522"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js", ["523", "524", "525", "526", "527"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js", ["528", "529", "530", "531"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js", ["532", "533", "534", "535", "536", "537", "538", "539", "540", "541"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js", ["542"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js", ["543", "544", "545", "546", "547", "548", "549", "550", "551", "552"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js", ["553", "554", "555", "556", "557", "558", "559", "560", "561", "562", "563", "564", "565", "566", "567"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCaviPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js", ["568"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js", ["569", "570", "571", "572", "573", "574", "575", "576"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js", ["577", "578", "579", "580", "581", "582", "583", "584", "585", "586", "587", "588", "589", "590", "591"], [], "C:\\CMS\\webapp\\frontend\\src\\config.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js", ["592"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx", ["593"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js", ["594", "595", "596"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js", ["597", "598", "599", "600"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js", ["601", "602"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js", ["603", "604"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\StoricoUtilizzoPage.js", ["605", "606", "607", "608"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js", ["609", "610", "611", "612"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\MetriPosatiSemplificatoPage.js", ["613"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaBobinaPage.js", ["614", "615", "616", "617", "618", "619", "620"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaCavoPage.js", ["621", "622", "623"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\InserisciMetriPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\CollegamentiPage.js", ["624", "625", "626", "627", "628", "629", "630"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js", ["631", "632"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js", ["633", "634"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js", ["635", "636"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js", ["637", "638"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCavi.js", ["639", "640", "641", "642", "643", "644", "645", "646"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js", ["647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\PosaCaviCollegamenti.js", ["661", "662", "663", "664", "665", "666", "667"], ["668"], "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js", ["669", "670", "671", "672", "673", "674", "675", "676", "677", "678", "679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689", "690", "691"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js", ["692", "693"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js", ["694", "695"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js", ["696", "697", "698", "699", "700", "701", "702", "703", "704", "705", "706"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js", ["707"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js", ["708", "709", "710", "711", "712", "713", "714", "715", "716", "717", "718", "719", "720", "721", "722", "723", "724", "725", "726", "727", "728", "729"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx", ["730"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx", ["731"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx", ["732"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js", ["733", "734", "735", "736", "737", "738", "739", "740", "741", "742", "743"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js", ["744"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js", ["745", "746", "747", "748"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaForm.js", ["749", "750", "751", "752", "753", "754", "755", "756", "757", "758", "759", "760", "761", "762", "763", "764", "765", "766", "767", "768", "769", "770", "771", "772"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriForm.js", ["773", "774", "775", "776", "777", "778", "779", "780", "781", "782", "783", "784", "785", "786", "787", "788"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js", ["789", "790"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js", ["791"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js", ["792", "793"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js", ["794", "795", "796", "797", "798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810", "811", "812", "813"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js", ["814", "815", "816", "817", "818", "819", "820", "821"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js", ["822"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js", ["823", "824", "825"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js", ["826", "827"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js", ["828"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js", ["829"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js", ["830"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCaviImproved.js", ["831", "832", "833", "834", "835", "836"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\comande\\ComandePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeList.js", ["837"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\TestComande.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\AccessoRapidoComanda.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCEI64_8Page.js", ["838", "839", "840", "841", "842", "843", "844", "845"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\CertificazioneCEI64_8.js", ["846", "847", "848", "849", "850", "851", "852"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\RapportiGenerali.js", ["853", "854", "855", "856", "857", "858", "859", "860", "861"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\ProveDettagliate.js", ["862", "863", "864", "865", "866", "867", "868", "869", "870"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\nonConformitaService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\rapportiGeneraliService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\proveDettagliateService.js", [], [], {"ruleId": "871", "severity": 1, "message": "872", "line": 12, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 12, "endColumn": 14}, {"ruleId": "875", "severity": 1, "message": "876", "line": 95, "column": 69, "nodeType": "877", "messageId": "878", "endLine": 95, "endColumn": 98}, {"ruleId": "875", "severity": 1, "message": "876", "line": 96, "column": 68, "nodeType": "877", "messageId": "878", "endLine": 96, "endColumn": 97}, {"ruleId": "875", "severity": 1, "message": "876", "line": 97, "column": 65, "nodeType": "877", "messageId": "878", "endLine": 97, "endColumn": 94}, {"ruleId": "875", "severity": 1, "message": "876", "line": 98, "column": 74, "nodeType": "877", "messageId": "878", "endLine": 98, "endColumn": 103}, {"ruleId": "875", "severity": 1, "message": "876", "line": 99, "column": 69, "nodeType": "877", "messageId": "878", "endLine": 99, "endColumn": 98}, {"ruleId": "879", "severity": 1, "message": "880", "line": 78, "column": 11, "nodeType": "881", "messageId": "882", "endLine": 78, "endColumn": 115}, {"ruleId": "879", "severity": 1, "message": "880", "line": 80, "column": 11, "nodeType": "881", "messageId": "882", "endLine": 80, "endColumn": 107}, {"ruleId": "879", "severity": 1, "message": "880", "line": 86, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 86, "endColumn": 105}, {"ruleId": "879", "severity": 1, "message": "880", "line": 89, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 89, "endColumn": 41}, {"ruleId": "871", "severity": 1, "message": "883", "line": 13, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 13, "endColumn": 9}, {"ruleId": "871", "severity": 1, "message": "884", "line": 20, "column": 25, "nodeType": "873", "messageId": "874", "endLine": 20, "endColumn": 34}, {"ruleId": "871", "severity": 1, "message": "885", "line": 21, "column": 19, "nodeType": "873", "messageId": "874", "endLine": 21, "endColumn": 35}, {"ruleId": "871", "severity": 1, "message": "886", "line": 22, "column": 12, "nodeType": "873", "messageId": "874", "endLine": 22, "endColumn": 21}, {"ruleId": "871", "severity": 1, "message": "887", "line": 23, "column": 18, "nodeType": "873", "messageId": "874", "endLine": 23, "endColumn": 28}, {"ruleId": "871", "severity": 1, "message": "888", "line": 56, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 56, "endColumn": 22}, {"ruleId": "871", "severity": 1, "message": "889", "line": 57, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 57, "endColumn": 23}, {"ruleId": "871", "severity": 1, "message": "890", "line": 58, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 58, "endColumn": 26}, {"ruleId": "871", "severity": 1, "message": "891", "line": 59, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 59, "endColumn": 22}, {"ruleId": "871", "severity": 1, "message": "892", "line": 68, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 68, "endColumn": 29}, {"ruleId": "871", "severity": 1, "message": "893", "line": 1, "column": 8, "nodeType": "873", "messageId": "874", "endLine": 1, "endColumn": 13}, {"ruleId": "871", "severity": 1, "message": "894", "line": 2, "column": 27, "nodeType": "873", "messageId": "874", "endLine": 2, "endColumn": 31}, {"ruleId": "871", "severity": 1, "message": "895", "line": 2, "column": 33, "nodeType": "873", "messageId": "874", "endLine": 2, "endColumn": 37}, {"ruleId": "871", "severity": 1, "message": "896", "line": 2, "column": 39, "nodeType": "873", "messageId": "874", "endLine": 2, "endColumn": 50}, {"ruleId": "871", "severity": 1, "message": "897", "line": 2, "column": 52, "nodeType": "873", "messageId": "874", "endLine": 2, "endColumn": 66}, {"ruleId": "871", "severity": 1, "message": "883", "line": 2, "column": 68, "nodeType": "873", "messageId": "874", "endLine": 2, "endColumn": 74}, {"ruleId": "871", "severity": 1, "message": "884", "line": 5, "column": 25, "nodeType": "873", "messageId": "874", "endLine": 5, "endColumn": 34}, {"ruleId": "871", "severity": 1, "message": "885", "line": 6, "column": 19, "nodeType": "873", "messageId": "874", "endLine": 6, "endColumn": 35}, {"ruleId": "871", "severity": 1, "message": "886", "line": 7, "column": 12, "nodeType": "873", "messageId": "874", "endLine": 7, "endColumn": 21}, {"ruleId": "871", "severity": 1, "message": "887", "line": 8, "column": 18, "nodeType": "873", "messageId": "874", "endLine": 8, "endColumn": 28}, {"ruleId": "871", "severity": 1, "message": "898", "line": 43, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 43, "endColumn": 19}, {"ruleId": "871", "severity": 1, "message": "895", "line": 8, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 8, "endColumn": 7}, {"ruleId": "871", "severity": 1, "message": "896", "line": 9, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 9, "endColumn": 14}, {"ruleId": "871", "severity": 1, "message": "872", "line": 10, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 10, "endColumn": 14}, {"ruleId": "871", "severity": 1, "message": "894", "line": 11, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 11, "endColumn": 7}, {"ruleId": "871", "severity": 1, "message": "899", "line": 12, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 12, "endColumn": 10}, {"ruleId": "871", "severity": 1, "message": "900", "line": 15, "column": 11, "nodeType": "873", "messageId": "874", "endLine": 15, "endColumn": 19}, {"ruleId": "871", "severity": 1, "message": "901", "line": 16, "column": 15, "nodeType": "873", "messageId": "874", "endLine": 16, "endColumn": 27}, {"ruleId": "871", "severity": 1, "message": "902", "line": 17, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 17, "endColumn": 17}, {"ruleId": "871", "severity": 1, "message": "903", "line": 18, "column": 11, "nodeType": "873", "messageId": "874", "endLine": 18, "endColumn": 19}, {"ruleId": "871", "severity": 1, "message": "904", "line": 19, "column": 13, "nodeType": "873", "messageId": "874", "endLine": 19, "endColumn": 23}, {"ruleId": "871", "severity": 1, "message": "905", "line": 20, "column": 14, "nodeType": "873", "messageId": "874", "endLine": 20, "endColumn": 25}, {"ruleId": "871", "severity": 1, "message": "906", "line": 25, "column": 8, "nodeType": "873", "messageId": "874", "endLine": 25, "endColumn": 17}, {"ruleId": "871", "severity": 1, "message": "907", "line": 28, "column": 11, "nodeType": "873", "messageId": "874", "endLine": 28, "endColumn": 26}, {"ruleId": "871", "severity": 1, "message": "908", "line": 48, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 48, "endColumn": 22}, {"ruleId": "871", "severity": 1, "message": "909", "line": 53, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 53, "endColumn": 20}, {"ruleId": "871", "severity": 1, "message": "910", "line": 11, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 11, "endColumn": 19}, {"ruleId": "871", "severity": 1, "message": "911", "line": 4, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 4, "endColumn": 13}, {"ruleId": "871", "severity": 1, "message": "912", "line": 5, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 5, "endColumn": 8}, {"ruleId": "871", "severity": 1, "message": "913", "line": 7, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 7, "endColumn": 13}, {"ruleId": "871", "severity": 1, "message": "914", "line": 12, "column": 14, "nodeType": "873", "messageId": "874", "endLine": 12, "endColumn": 25}, {"ruleId": "871", "severity": 1, "message": "900", "line": 13, "column": 11, "nodeType": "873", "messageId": "874", "endLine": 13, "endColumn": 19}, {"ruleId": "871", "severity": 1, "message": "915", "line": 17, "column": 8, "nodeType": "873", "messageId": "874", "endLine": 17, "endColumn": 23}, {"ruleId": "871", "severity": 1, "message": "907", "line": 21, "column": 11, "nodeType": "873", "messageId": "874", "endLine": 21, "endColumn": 26}, {"ruleId": "871", "severity": 1, "message": "916", "line": 26, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 26, "endColumn": 21}, {"ruleId": "871", "severity": 1, "message": "895", "line": 8, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 8, "endColumn": 7}, {"ruleId": "871", "severity": 1, "message": "896", "line": 9, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 9, "endColumn": 14}, {"ruleId": "871", "severity": 1, "message": "913", "line": 11, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 11, "endColumn": 13}, {"ruleId": "871", "severity": 1, "message": "917", "line": 14, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 14, "endColumn": 17}, {"ruleId": "871", "severity": 1, "message": "918", "line": 25, "column": 8, "nodeType": "873", "messageId": "874", "endLine": 25, "endColumn": 16}, {"ruleId": "871", "severity": 1, "message": "919", "line": 31, "column": 8, "nodeType": "873", "messageId": "874", "endLine": 31, "endColumn": 16}, {"ruleId": "871", "severity": 1, "message": "907", "line": 37, "column": 11, "nodeType": "873", "messageId": "874", "endLine": 37, "endColumn": 26}, {"ruleId": "871", "severity": 1, "message": "920", "line": 39, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 39, "endColumn": 17}, {"ruleId": "871", "severity": 1, "message": "916", "line": 41, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 41, "endColumn": 22}, {"ruleId": "871", "severity": 1, "message": "921", "line": 109, "column": 19, "nodeType": "873", "messageId": "874", "endLine": 109, "endColumn": 29}, {"ruleId": "871", "severity": 1, "message": "922", "line": 117, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 117, "endColumn": 28}, {"ruleId": "871", "severity": 1, "message": "923", "line": 118, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 118, "endColumn": 23}, {"ruleId": "871", "severity": 1, "message": "924", "line": 118, "column": 25, "nodeType": "873", "messageId": "874", "endLine": 118, "endColumn": 41}, {"ruleId": "925", "severity": 1, "message": "926", "line": 472, "column": 6, "nodeType": "927", "endLine": 472, "endColumn": 15, "suggestions": "928"}, {"ruleId": "871", "severity": 1, "message": "929", "line": 477, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 477, "endColumn": 26}, {"ruleId": "871", "severity": 1, "message": "930", "line": 1, "column": 27, "nodeType": "873", "messageId": "874", "endLine": 1, "endColumn": 36}, {"ruleId": "871", "severity": 1, "message": "931", "line": 49, "column": 19, "nodeType": "873", "messageId": "874", "endLine": 49, "endColumn": 26}, {"ruleId": "871", "severity": 1, "message": "899", "line": 15, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 15, "endColumn": 10}, {"ruleId": "871", "severity": 1, "message": "914", "line": 39, "column": 14, "nodeType": "873", "messageId": "874", "endLine": 39, "endColumn": 25}, {"ruleId": "871", "severity": 1, "message": "932", "line": 43, "column": 16, "nodeType": "873", "messageId": "874", "endLine": 43, "endColumn": 29}, {"ruleId": "871", "severity": 1, "message": "911", "line": 4, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 4, "endColumn": 13}, {"ruleId": "871", "severity": 1, "message": "912", "line": 5, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 5, "endColumn": 8}, {"ruleId": "871", "severity": 1, "message": "916", "line": 26, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 26, "endColumn": 21}, {"ruleId": "871", "severity": 1, "message": "933", "line": 48, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 48, "endColumn": 29}, {"ruleId": "871", "severity": 1, "message": "911", "line": 4, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 4, "endColumn": 13}, {"ruleId": "871", "severity": 1, "message": "933", "line": 37, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 37, "endColumn": 29}, {"ruleId": "871", "severity": 1, "message": "911", "line": 4, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 4, "endColumn": 13}, {"ruleId": "871", "severity": 1, "message": "933", "line": 52, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 52, "endColumn": 29}, {"ruleId": "871", "severity": 1, "message": "911", "line": 4, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 4, "endColumn": 13}, {"ruleId": "871", "severity": 1, "message": "912", "line": 5, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 5, "endColumn": 8}, {"ruleId": "871", "severity": 1, "message": "916", "line": 26, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 26, "endColumn": 21}, {"ruleId": "871", "severity": 1, "message": "933", "line": 48, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 48, "endColumn": 29}, {"ruleId": "871", "severity": 1, "message": "911", "line": 4, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 4, "endColumn": 13}, {"ruleId": "871", "severity": 1, "message": "912", "line": 5, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 5, "endColumn": 8}, {"ruleId": "871", "severity": 1, "message": "916", "line": 26, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 26, "endColumn": 21}, {"ruleId": "871", "severity": 1, "message": "933", "line": 48, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 48, "endColumn": 29}, {"ruleId": "871", "severity": 1, "message": "916", "line": 27, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 27, "endColumn": 21}, {"ruleId": "871", "severity": 1, "message": "912", "line": 5, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 5, "endColumn": 8}, {"ruleId": "871", "severity": 1, "message": "934", "line": 6, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 6, "endColumn": 9}, {"ruleId": "871", "severity": 1, "message": "900", "line": 14, "column": 11, "nodeType": "873", "messageId": "874", "endLine": 14, "endColumn": 19}, {"ruleId": "871", "severity": 1, "message": "907", "line": 23, "column": 11, "nodeType": "873", "messageId": "874", "endLine": 23, "endColumn": 26}, {"ruleId": "871", "severity": 1, "message": "916", "line": 30, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 30, "endColumn": 21}, {"ruleId": "871", "severity": 1, "message": "933", "line": 33, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 33, "endColumn": 29}, {"ruleId": "871", "severity": 1, "message": "935", "line": 38, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 38, "endColumn": 26}, {"ruleId": "871", "severity": 1, "message": "907", "line": 20, "column": 11, "nodeType": "873", "messageId": "874", "endLine": 20, "endColumn": 26}, {"ruleId": "871", "severity": 1, "message": "916", "line": 27, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 27, "endColumn": 21}, {"ruleId": "871", "severity": 1, "message": "935", "line": 35, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 35, "endColumn": 26}, {"ruleId": "871", "severity": 1, "message": "912", "line": 5, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 5, "endColumn": 8}, {"ruleId": "871", "severity": 1, "message": "934", "line": 6, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 6, "endColumn": 9}, {"ruleId": "871", "severity": 1, "message": "900", "line": 14, "column": 11, "nodeType": "873", "messageId": "874", "endLine": 14, "endColumn": 19}, {"ruleId": "871", "severity": 1, "message": "907", "line": 23, "column": 11, "nodeType": "873", "messageId": "874", "endLine": 23, "endColumn": 26}, {"ruleId": "871", "severity": 1, "message": "916", "line": 30, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 30, "endColumn": 21}, {"ruleId": "871", "severity": 1, "message": "933", "line": 33, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 33, "endColumn": 29}, {"ruleId": "871", "severity": 1, "message": "935", "line": 38, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 38, "endColumn": 26}, {"ruleId": "871", "severity": 1, "message": "907", "line": 24, "column": 11, "nodeType": "873", "messageId": "874", "endLine": 24, "endColumn": 26}, {"ruleId": "925", "severity": 1, "message": "936", "line": 53, "column": 6, "nodeType": "927", "endLine": 53, "endColumn": 18, "suggestions": "937"}, {"ruleId": "871", "severity": 1, "message": "938", "line": 1, "column": 8, "nodeType": "873", "messageId": "874", "endLine": 1, "endColumn": 13}, {"ruleId": "871", "severity": 1, "message": "939", "line": 5, "column": 7, "nodeType": "873", "messageId": "874", "endLine": 5, "endColumn": 14}, {"ruleId": "871", "severity": 1, "message": "899", "line": 14, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 14, "endColumn": 10}, {"ruleId": "871", "severity": 1, "message": "940", "line": 28, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 28, "endColumn": 18}, {"ruleId": "871", "severity": 1, "message": "938", "line": 1, "column": 8, "nodeType": "873", "messageId": "874", "endLine": 1, "endColumn": 13}, {"ruleId": "871", "severity": 1, "message": "939", "line": 5, "column": 7, "nodeType": "873", "messageId": "874", "endLine": 5, "endColumn": 14}, {"ruleId": "871", "severity": 1, "message": "895", "line": 8, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 8, "endColumn": 7}, {"ruleId": "871", "severity": 1, "message": "896", "line": 9, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 9, "endColumn": 14}, {"ruleId": "871", "severity": 1, "message": "872", "line": 10, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 10, "endColumn": 14}, {"ruleId": "871", "severity": 1, "message": "941", "line": 23, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 23, "endColumn": 15}, {"ruleId": "871", "severity": 1, "message": "942", "line": 24, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 24, "endColumn": 17}, {"ruleId": "871", "severity": 1, "message": "901", "line": 46, "column": 15, "nodeType": "873", "messageId": "874", "endLine": 46, "endColumn": 27}, {"ruleId": "871", "severity": 1, "message": "943", "line": 47, "column": 12, "nodeType": "873", "messageId": "874", "endLine": 47, "endColumn": 21}, {"ruleId": "925", "severity": 1, "message": "944", "line": 134, "column": 6, "nodeType": "927", "endLine": 134, "endColumn": 18, "suggestions": "945"}, {"ruleId": "871", "severity": 1, "message": "895", "line": 8, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 8, "endColumn": 7}, {"ruleId": "871", "severity": 1, "message": "896", "line": 9, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 9, "endColumn": 14}, {"ruleId": "871", "severity": 1, "message": "872", "line": 10, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 10, "endColumn": 14}, {"ruleId": "871", "severity": 1, "message": "941", "line": 23, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 23, "endColumn": 15}, {"ruleId": "871", "severity": 1, "message": "942", "line": 24, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 24, "endColumn": 17}, {"ruleId": "871", "severity": 1, "message": "899", "line": 25, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 25, "endColumn": 10}, {"ruleId": "871", "severity": 1, "message": "913", "line": 29, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 29, "endColumn": 13}, {"ruleId": "871", "severity": 1, "message": "903", "line": 39, "column": 11, "nodeType": "873", "messageId": "874", "endLine": 39, "endColumn": 19}, {"ruleId": "871", "severity": 1, "message": "901", "line": 43, "column": 15, "nodeType": "873", "messageId": "874", "endLine": 43, "endColumn": 27}, {"ruleId": "871", "severity": 1, "message": "946", "line": 44, "column": 14, "nodeType": "873", "messageId": "874", "endLine": 44, "endColumn": 25}, {"ruleId": "871", "severity": 1, "message": "947", "line": 50, "column": 69, "nodeType": "873", "messageId": "874", "endLine": 50, "endColumn": 76}, {"ruleId": "871", "severity": 1, "message": "948", "line": 79, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 79, "endColumn": 26}, {"ruleId": "925", "severity": 1, "message": "949", "line": 145, "column": 6, "nodeType": "927", "endLine": 145, "endColumn": 8, "suggestions": "950"}, {"ruleId": "871", "severity": 1, "message": "951", "line": 689, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 689, "endColumn": 26}, {"ruleId": "871", "severity": 1, "message": "952", "line": 20, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 20, "endColumn": 13}, {"ruleId": "871", "severity": 1, "message": "953", "line": 21, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 21, "endColumn": 9}, {"ruleId": "871", "severity": 1, "message": "954", "line": 22, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 22, "endColumn": 11}, {"ruleId": "871", "severity": 1, "message": "894", "line": 23, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 23, "endColumn": 7}, {"ruleId": "871", "severity": 1, "message": "955", "line": 26, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 26, "endColumn": 17}, {"ruleId": "871", "severity": 1, "message": "956", "line": 69, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 69, "endColumn": 22}, {"ruleId": "957", "severity": 1, "message": "958", "line": 466, "column": 9, "nodeType": "959", "messageId": "960", "endLine": 469, "endColumn": 10}, {"ruleId": "925", "severity": 1, "message": "961", "line": 95, "column": 6, "nodeType": "927", "endLine": 95, "endColumn": 21, "suggestions": "962", "suppressions": "963"}, {"ruleId": "879", "severity": 1, "message": "880", "line": 260, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 264, "endColumn": 11}, {"ruleId": "879", "severity": 1, "message": "880", "line": 274, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 274, "endColumn": 70}, {"ruleId": "879", "severity": 1, "message": "880", "line": 278, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 278, "endColumn": 54}, {"ruleId": "879", "severity": 1, "message": "880", "line": 333, "column": 11, "nodeType": "881", "messageId": "882", "endLine": 338, "endColumn": 13}, {"ruleId": "879", "severity": 1, "message": "880", "line": 435, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 439, "endColumn": 11}, {"ruleId": "879", "severity": 1, "message": "880", "line": 451, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 451, "endColumn": 54}, {"ruleId": "879", "severity": 1, "message": "880", "line": 668, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 668, "endColumn": 163}, {"ruleId": "879", "severity": 1, "message": "880", "line": 677, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 677, "endColumn": 70}, {"ruleId": "879", "severity": 1, "message": "880", "line": 681, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 681, "endColumn": 54}, {"ruleId": "871", "severity": 1, "message": "964", "line": 755, "column": 17, "nodeType": "873", "messageId": "874", "endLine": 755, "endColumn": 22}, {"ruleId": "879", "severity": 1, "message": "880", "line": 775, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 779, "endColumn": 11}, {"ruleId": "879", "severity": 1, "message": "880", "line": 794, "column": 11, "nodeType": "881", "messageId": "882", "endLine": 798, "endColumn": 13}, {"ruleId": "879", "severity": 1, "message": "880", "line": 801, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 804, "endColumn": 11}, {"ruleId": "879", "severity": 1, "message": "880", "line": 810, "column": 11, "nodeType": "881", "messageId": "882", "endLine": 814, "endColumn": 13}, {"ruleId": "879", "severity": 1, "message": "880", "line": 817, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 820, "endColumn": 11}, {"ruleId": "879", "severity": 1, "message": "880", "line": 885, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 889, "endColumn": 11}, {"ruleId": "965", "severity": 1, "message": "966", "line": 955, "column": 3, "nodeType": "967", "messageId": "968", "endLine": 955, "endColumn": 29}, {"ruleId": "965", "severity": 1, "message": "969", "line": 1143, "column": 3, "nodeType": "967", "messageId": "968", "endLine": 1143, "endColumn": 23}, {"ruleId": "965", "severity": 1, "message": "970", "line": 1238, "column": 3, "nodeType": "967", "messageId": "968", "endLine": 1238, "endColumn": 20}, {"ruleId": "879", "severity": 1, "message": "880", "line": 1287, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 1287, "endColumn": 163}, {"ruleId": "879", "severity": 1, "message": "880", "line": 1317, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 1317, "endColumn": 163}, {"ruleId": "879", "severity": 1, "message": "880", "line": 1370, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 1370, "endColumn": 163}, {"ruleId": "879", "severity": 1, "message": "880", "line": 1412, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 1412, "endColumn": 163}, {"ruleId": "871", "severity": 1, "message": "971", "line": 6, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 6, "endColumn": 8}, {"ruleId": "871", "severity": 1, "message": "899", "line": 11, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 11, "endColumn": 10}, {"ruleId": "871", "severity": 1, "message": "938", "line": 1, "column": 8, "nodeType": "873", "messageId": "874", "endLine": 1, "endColumn": 13}, {"ruleId": "871", "severity": 1, "message": "939", "line": 5, "column": 7, "nodeType": "873", "messageId": "874", "endLine": 5, "endColumn": 14}, {"ruleId": "871", "severity": 1, "message": "972", "line": 3, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 3, "endColumn": 11}, {"ruleId": "871", "severity": 1, "message": "973", "line": 4, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 4, "endColumn": 6}, {"ruleId": "871", "severity": 1, "message": "974", "line": 5, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 5, "endColumn": 7}, {"ruleId": "871", "severity": 1, "message": "975", "line": 6, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 6, "endColumn": 11}, {"ruleId": "871", "severity": 1, "message": "976", "line": 7, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 7, "endColumn": 6}, {"ruleId": "871", "severity": 1, "message": "977", "line": 12, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 12, "endColumn": 9}, {"ruleId": "871", "severity": 1, "message": "978", "line": 36, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 36, "endColumn": 21}, {"ruleId": "871", "severity": 1, "message": "979", "line": 50, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 50, "endColumn": 17}, {"ruleId": "871", "severity": 1, "message": "980", "line": 64, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 64, "endColumn": 20}, {"ruleId": "871", "severity": 1, "message": "981", "line": 88, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 88, "endColumn": 22}, {"ruleId": "871", "severity": 1, "message": "982", "line": 104, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 104, "endColumn": 30}, {"ruleId": "871", "severity": 1, "message": "983", "line": 3, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 3, "endColumn": 12}, {"ruleId": "871", "severity": 1, "message": "975", "line": 3, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 3, "endColumn": 11}, {"ruleId": "871", "severity": 1, "message": "976", "line": 4, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 4, "endColumn": 6}, {"ruleId": "871", "severity": 1, "message": "984", "line": 5, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 5, "endColumn": 8}, {"ruleId": "871", "severity": 1, "message": "985", "line": 6, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 6, "endColumn": 8}, {"ruleId": "871", "severity": 1, "message": "986", "line": 7, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 7, "endColumn": 16}, {"ruleId": "871", "severity": 1, "message": "987", "line": 8, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 8, "endColumn": 10}, {"ruleId": "871", "severity": 1, "message": "977", "line": 9, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 9, "endColumn": 9}, {"ruleId": "871", "severity": 1, "message": "988", "line": 10, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 10, "endColumn": 22}, {"ruleId": "871", "severity": 1, "message": "972", "line": 11, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 11, "endColumn": 11}, {"ruleId": "871", "severity": 1, "message": "973", "line": 12, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 12, "endColumn": 6}, {"ruleId": "871", "severity": 1, "message": "974", "line": 13, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 13, "endColumn": 7}, {"ruleId": "871", "severity": 1, "message": "989", "line": 14, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 14, "endColumn": 16}, {"ruleId": "871", "severity": 1, "message": "990", "line": 15, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 15, "endColumn": 7}, {"ruleId": "871", "severity": 1, "message": "983", "line": 16, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 16, "endColumn": 12}, {"ruleId": "871", "severity": 1, "message": "991", "line": 18, "column": 40, "nodeType": "873", "messageId": "874", "endLine": 18, "endColumn": 44}, {"ruleId": "871", "severity": 1, "message": "992", "line": 47, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 47, "endColumn": 19}, {"ruleId": "871", "severity": 1, "message": "993", "line": 64, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 64, "endColumn": 19}, {"ruleId": "871", "severity": 1, "message": "994", "line": 71, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 71, "endColumn": 20}, {"ruleId": "871", "severity": 1, "message": "981", "line": 79, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 79, "endColumn": 22}, {"ruleId": "871", "severity": 1, "message": "982", "line": 95, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 95, "endColumn": 30}, {"ruleId": "871", "severity": 1, "message": "995", "line": 299, "column": 27, "nodeType": "873", "messageId": "874", "endLine": 299, "endColumn": 37}, {"ruleId": "871", "severity": 1, "message": "996", "line": 300, "column": 27, "nodeType": "873", "messageId": "874", "endLine": 300, "endColumn": 36}, {"ruleId": "871", "severity": 1, "message": "912", "line": 3, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 3, "endColumn": 8}, {"ruleId": "925", "severity": 1, "message": "997", "line": 54, "column": 6, "nodeType": "927", "endLine": 54, "endColumn": 34, "suggestions": "998"}, {"ruleId": "871", "severity": 1, "message": "999", "line": 25, "column": 13, "nodeType": "873", "messageId": "874", "endLine": 25, "endColumn": 25}, {"ruleId": "871", "severity": 1, "message": "1000", "line": 33, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 33, "endColumn": 15}, {"ruleId": "871", "severity": 1, "message": "1001", "line": 34, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 34, "endColumn": 14}, {"ruleId": "871", "severity": 1, "message": "1002", "line": 35, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 35, "endColumn": 22}, {"ruleId": "871", "severity": 1, "message": "1003", "line": 36, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 36, "endColumn": 21}, {"ruleId": "871", "severity": 1, "message": "1004", "line": 37, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 37, "endColumn": 17}, {"ruleId": "871", "severity": 1, "message": "1005", "line": 41, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 41, "endColumn": 20}, {"ruleId": "871", "severity": 1, "message": "1006", "line": 43, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 43, "endColumn": 34}, {"ruleId": "871", "severity": 1, "message": "1007", "line": 69, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 69, "endColumn": 17}, {"ruleId": "871", "severity": 1, "message": "1008", "line": 69, "column": 19, "nodeType": "873", "messageId": "874", "endLine": 69, "endColumn": 29}, {"ruleId": "925", "severity": 1, "message": "1009", "line": 88, "column": 6, "nodeType": "927", "endLine": 88, "endColumn": 18, "suggestions": "1010"}, {"ruleId": "925", "severity": 1, "message": "1011", "line": 448, "column": 6, "nodeType": "927", "endLine": 448, "endColumn": 28, "suggestions": "1012"}, {"ruleId": "871", "severity": 1, "message": "1013", "line": 4, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 4, "endColumn": 12}, {"ruleId": "871", "severity": 1, "message": "1014", "line": 8, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 8, "endColumn": 7}, {"ruleId": "871", "severity": 1, "message": "1015", "line": 9, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 9, "endColumn": 11}, {"ruleId": "871", "severity": 1, "message": "1016", "line": 10, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 10, "endColumn": 15}, {"ruleId": "925", "severity": 1, "message": "997", "line": 46, "column": 6, "nodeType": "927", "endLine": 46, "endColumn": 18, "suggestions": "1017"}, {"ruleId": "871", "severity": 1, "message": "1018", "line": 9, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 9, "endColumn": 14}, {"ruleId": "871", "severity": 1, "message": "952", "line": 10, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 10, "endColumn": 13}, {"ruleId": "871", "severity": 1, "message": "953", "line": 11, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 11, "endColumn": 9}, {"ruleId": "871", "severity": 1, "message": "954", "line": 12, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 12, "endColumn": 11}, {"ruleId": "871", "severity": 1, "message": "1016", "line": 33, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 33, "endColumn": 15}, {"ruleId": "871", "severity": 1, "message": "1019", "line": 35, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 35, "endColumn": 26}, {"ruleId": "871", "severity": 1, "message": "946", "line": 42, "column": 14, "nodeType": "873", "messageId": "874", "endLine": 42, "endColumn": 25}, {"ruleId": "871", "severity": 1, "message": "1000", "line": 52, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 52, "endColumn": 15}, {"ruleId": "871", "severity": 1, "message": "1001", "line": 53, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 53, "endColumn": 14}, {"ruleId": "871", "severity": 1, "message": "1002", "line": 54, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 54, "endColumn": 22}, {"ruleId": "871", "severity": 1, "message": "1003", "line": 55, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 55, "endColumn": 21}, {"ruleId": "871", "severity": 1, "message": "1004", "line": 56, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 56, "endColumn": 17}, {"ruleId": "871", "severity": 1, "message": "1020", "line": 57, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 57, "endColumn": 15}, {"ruleId": "871", "severity": 1, "message": "1021", "line": 58, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 58, "endColumn": 19}, {"ruleId": "871", "severity": 1, "message": "1022", "line": 59, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 59, "endColumn": 21}, {"ruleId": "871", "severity": 1, "message": "920", "line": 72, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 72, "endColumn": 17}, {"ruleId": "871", "severity": 1, "message": "1023", "line": 79, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 79, "endColumn": 23}, {"ruleId": "871", "severity": 1, "message": "1024", "line": 79, "column": 25, "nodeType": "873", "messageId": "874", "endLine": 79, "endColumn": 41}, {"ruleId": "871", "severity": 1, "message": "1025", "line": 80, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 80, "endColumn": 27}, {"ruleId": "871", "severity": 1, "message": "1026", "line": 85, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 85, "endColumn": 26}, {"ruleId": "925", "severity": 1, "message": "997", "line": 105, "column": 6, "nodeType": "927", "endLine": 105, "endColumn": 18, "suggestions": "1027"}, {"ruleId": "925", "severity": 1, "message": "1028", "line": 112, "column": 6, "nodeType": "927", "endLine": 112, "endColumn": 20, "suggestions": "1029"}, {"ruleId": "925", "severity": 1, "message": "1030", "line": 127, "column": 6, "nodeType": "927", "endLine": 127, "endColumn": 34, "suggestions": "1031"}, {"ruleId": "871", "severity": 1, "message": "1032", "line": 283, "column": 13, "nodeType": "873", "messageId": "874", "endLine": 283, "endColumn": 19}, {"ruleId": "871", "severity": 1, "message": "955", "line": 17, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 17, "endColumn": 17}, {"ruleId": "871", "severity": 1, "message": "1016", "line": 34, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 34, "endColumn": 15}, {"ruleId": "871", "severity": 1, "message": "1019", "line": 35, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 35, "endColumn": 26}, {"ruleId": "871", "severity": 1, "message": "1033", "line": 39, "column": 11, "nodeType": "873", "messageId": "874", "endLine": 39, "endColumn": 19}, {"ruleId": "871", "severity": 1, "message": "1000", "line": 51, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 51, "endColumn": 15}, {"ruleId": "871", "severity": 1, "message": "1001", "line": 52, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 52, "endColumn": 14}, {"ruleId": "871", "severity": 1, "message": "1003", "line": 54, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 54, "endColumn": 21}, {"ruleId": "871", "severity": 1, "message": "1004", "line": 55, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 55, "endColumn": 17}, {"ruleId": "871", "severity": 1, "message": "1006", "line": 62, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 62, "endColumn": 34}, {"ruleId": "871", "severity": 1, "message": "1034", "line": 105, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 105, "endColumn": 26}, {"ruleId": "871", "severity": 1, "message": "1035", "line": 105, "column": 28, "nodeType": "873", "messageId": "874", "endLine": 105, "endColumn": 47}, {"ruleId": "925", "severity": 1, "message": "1028", "line": 145, "column": 6, "nodeType": "927", "endLine": 145, "endColumn": 18, "suggestions": "1036"}, {"ruleId": "871", "severity": 1, "message": "1037", "line": 701, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 701, "endColumn": 19}, {"ruleId": "871", "severity": 1, "message": "1038", "line": 1311, "column": 11, "nodeType": "873", "messageId": "874", "endLine": 1311, "endColumn": 28}, {"ruleId": "871", "severity": 1, "message": "1039", "line": 1316, "column": 11, "nodeType": "873", "messageId": "874", "endLine": 1316, "endColumn": 30}, {"ruleId": "871", "severity": 1, "message": "1040", "line": 1883, "column": 9, "nodeType": "873", "messageId": "874", "endLine": 1883, "endColumn": 23}, {"ruleId": "871", "severity": 1, "message": "938", "line": 1, "column": 8, "nodeType": "873", "messageId": "874", "endLine": 1, "endColumn": 13}, {"ruleId": "871", "severity": 1, "message": "939", "line": 5, "column": 7, "nodeType": "873", "messageId": "874", "endLine": 5, "endColumn": 14}, {"ruleId": "871", "severity": 1, "message": "1041", "line": 1, "column": 8, "nodeType": "873", "messageId": "874", "endLine": 1, "endColumn": 14}, {"ruleId": "871", "severity": 1, "message": "938", "line": 1, "column": 8, "nodeType": "873", "messageId": "874", "endLine": 1, "endColumn": 13}, {"ruleId": "871", "severity": 1, "message": "939", "line": 5, "column": 7, "nodeType": "873", "messageId": "874", "endLine": 5, "endColumn": 14}, {"ruleId": "871", "severity": 1, "message": "938", "line": 1, "column": 8, "nodeType": "873", "messageId": "874", "endLine": 1, "endColumn": 13}, {"ruleId": "871", "severity": 1, "message": "939", "line": 5, "column": 7, "nodeType": "873", "messageId": "874", "endLine": 5, "endColumn": 14}, {"ruleId": "871", "severity": 1, "message": "1042", "line": 83, "column": 13, "nodeType": "873", "messageId": "874", "endLine": 83, "endColumn": 21}, {"ruleId": "879", "severity": 1, "message": "880", "line": 109, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 109, "endColumn": 163}, {"ruleId": "879", "severity": 1, "message": "880", "line": 123, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 123, "endColumn": 70}, {"ruleId": "879", "severity": 1, "message": "880", "line": 127, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 127, "endColumn": 54}, {"ruleId": "879", "severity": 1, "message": "880", "line": 212, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 212, "endColumn": 163}, {"ruleId": "879", "severity": 1, "message": "880", "line": 226, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 226, "endColumn": 70}, {"ruleId": "879", "severity": 1, "message": "880", "line": 230, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 230, "endColumn": 54}, {"ruleId": "879", "severity": 1, "message": "880", "line": 271, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 271, "endColumn": 163}, {"ruleId": "879", "severity": 1, "message": "880", "line": 280, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 280, "endColumn": 70}, {"ruleId": "879", "severity": 1, "message": "880", "line": 284, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 284, "endColumn": 54}, {"ruleId": "879", "severity": 1, "message": "880", "line": 320, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 320, "endColumn": 70}, {"ruleId": "879", "severity": 1, "message": "880", "line": 324, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 324, "endColumn": 54}, {"ruleId": "879", "severity": 1, "message": "880", "line": 360, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 360, "endColumn": 163}, {"ruleId": "879", "severity": 1, "message": "880", "line": 369, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 369, "endColumn": 70}, {"ruleId": "879", "severity": 1, "message": "880", "line": 373, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 373, "endColumn": 54}, {"ruleId": "879", "severity": 1, "message": "880", "line": 450, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 450, "endColumn": 163}, {"ruleId": "879", "severity": 1, "message": "880", "line": 459, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 459, "endColumn": 70}, {"ruleId": "879", "severity": 1, "message": "880", "line": 463, "column": 9, "nodeType": "881", "messageId": "882", "endLine": 463, "endColumn": 54}, {"ruleId": "871", "severity": 1, "message": "1043", "line": 12, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 12, "endColumn": 19}, {"ruleId": "871", "severity": 1, "message": "902", "line": 27, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 27, "endColumn": 17}, {"ruleId": "871", "severity": 1, "message": "918", "line": 30, "column": 11, "nodeType": "873", "messageId": "874", "endLine": 30, "endColumn": 19}, {"ruleId": "871", "severity": 1, "message": "1002", "line": 34, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 34, "endColumn": 29}, {"ruleId": "871", "severity": 1, "message": "1007", "line": 49, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 49, "endColumn": 17}, {"ruleId": "871", "severity": 1, "message": "1008", "line": 49, "column": 19, "nodeType": "873", "messageId": "874", "endLine": 49, "endColumn": 29}, {"ruleId": "925", "severity": 1, "message": "997", "line": 64, "column": 6, "nodeType": "927", "endLine": 64, "endColumn": 32, "suggestions": "1044"}, {"ruleId": "871", "severity": 1, "message": "1045", "line": 270, "column": 17, "nodeType": "873", "messageId": "874", "endLine": 270, "endColumn": 23}, {"ruleId": "871", "severity": 1, "message": "1046", "line": 17, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 17, "endColumn": 8}, {"ruleId": "871", "severity": 1, "message": "954", "line": 16, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 16, "endColumn": 11}, {"ruleId": "871", "severity": 1, "message": "953", "line": 17, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 17, "endColumn": 9}, {"ruleId": "871", "severity": 1, "message": "952", "line": 19, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 19, "endColumn": 13}, {"ruleId": "871", "severity": 1, "message": "918", "line": 14, "column": 11, "nodeType": "873", "messageId": "874", "endLine": 14, "endColumn": 19}, {"ruleId": "871", "severity": 1, "message": "1047", "line": 43, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 43, "endColumn": 26}, {"ruleId": "871", "severity": 1, "message": "913", "line": 12, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 12, "endColumn": 13}, {"ruleId": "871", "severity": 1, "message": "1048", "line": 3, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 3, "endColumn": 6}, {"ruleId": "871", "severity": 1, "message": "899", "line": 9, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 9, "endColumn": 10}, {"ruleId": "871", "severity": 1, "message": "1049", "line": 20, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 20, "endColumn": 19}, {"ruleId": "925", "severity": 1, "message": "1050", "line": 135, "column": 6, "nodeType": "927", "endLine": 135, "endColumn": 18, "suggestions": "1051"}, {"ruleId": "925", "severity": 1, "message": "1052", "line": 140, "column": 6, "nodeType": "927", "endLine": 140, "endColumn": 52, "suggestions": "1053"}, {"ruleId": "925", "severity": 1, "message": "1054", "line": 145, "column": 6, "nodeType": "927", "endLine": 145, "endColumn": 62, "suggestions": "1055"}, {"ruleId": "925", "severity": 1, "message": "1056", "line": 150, "column": 6, "nodeType": "927", "endLine": 150, "endColumn": 28, "suggestions": "1057"}, {"ruleId": "925", "severity": 1, "message": "1058", "line": 159, "column": 6, "nodeType": "927", "endLine": 159, "endColumn": 39, "suggestions": "1059"}, {"ruleId": "925", "severity": 1, "message": "1060", "line": 66, "column": 6, "nodeType": "927", "endLine": 66, "endColumn": 18, "suggestions": "1061"}, {"ruleId": "871", "severity": 1, "message": "895", "line": 8, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 8, "endColumn": 7}, {"ruleId": "871", "severity": 1, "message": "896", "line": 9, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 9, "endColumn": 14}, {"ruleId": "871", "severity": 1, "message": "934", "line": 10, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 10, "endColumn": 9}, {"ruleId": "871", "severity": 1, "message": "1015", "line": 12, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 12, "endColumn": 11}, {"ruleId": "871", "severity": 1, "message": "1062", "line": 21, "column": 17, "nodeType": "873", "messageId": "874", "endLine": 21, "endColumn": 31}, {"ruleId": "871", "severity": 1, "message": "887", "line": 24, "column": 17, "nodeType": "873", "messageId": "874", "endLine": 24, "endColumn": 27}, {"ruleId": "925", "severity": 1, "message": "1063", "line": 180, "column": 6, "nodeType": "927", "endLine": 180, "endColumn": 25, "suggestions": "1064"}, {"ruleId": "875", "severity": 1, "message": "1065", "line": 243, "column": 15, "nodeType": "877", "messageId": "878", "endLine": 248, "endColumn": 17}, {"ruleId": "871", "severity": 1, "message": "912", "line": 5, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 5, "endColumn": 8}, {"ruleId": "871", "severity": 1, "message": "942", "line": 15, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 15, "endColumn": 17}, {"ruleId": "871", "severity": 1, "message": "899", "line": 16, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 16, "endColumn": 10}, {"ruleId": "871", "severity": 1, "message": "903", "line": 28, "column": 11, "nodeType": "873", "messageId": "874", "endLine": 28, "endColumn": 19}, {"ruleId": "871", "severity": 1, "message": "904", "line": 29, "column": 13, "nodeType": "873", "messageId": "874", "endLine": 29, "endColumn": 23}, {"ruleId": "871", "severity": 1, "message": "1066", "line": 39, "column": 34, "nodeType": "873", "messageId": "874", "endLine": 39, "endColumn": 59}, {"ruleId": "871", "severity": 1, "message": "1007", "line": 41, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 41, "endColumn": 17}, {"ruleId": "871", "severity": 1, "message": "930", "line": 1, "column": 27, "nodeType": "873", "messageId": "874", "endLine": 1, "endColumn": 36}, {"ruleId": "871", "severity": 1, "message": "895", "line": 10, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 10, "endColumn": 7}, {"ruleId": "871", "severity": 1, "message": "896", "line": 11, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 11, "endColumn": 14}, {"ruleId": "871", "severity": 1, "message": "911", "line": 12, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 12, "endColumn": 13}, {"ruleId": "871", "severity": 1, "message": "1046", "line": 27, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 27, "endColumn": 8}, {"ruleId": "871", "severity": 1, "message": "902", "line": 30, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 30, "endColumn": 17}, {"ruleId": "871", "severity": 1, "message": "1067", "line": 33, "column": 17, "nodeType": "873", "messageId": "874", "endLine": 33, "endColumn": 25}, {"ruleId": "871", "severity": 1, "message": "887", "line": 34, "column": 17, "nodeType": "873", "messageId": "874", "endLine": 34, "endColumn": 27}, {"ruleId": "871", "severity": 1, "message": "914", "line": 35, "column": 14, "nodeType": "873", "messageId": "874", "endLine": 35, "endColumn": 25}, {"ruleId": "871", "severity": 1, "message": "895", "line": 10, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 10, "endColumn": 7}, {"ruleId": "871", "severity": 1, "message": "896", "line": 11, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 11, "endColumn": 14}, {"ruleId": "871", "severity": 1, "message": "1046", "line": 27, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 27, "endColumn": 8}, {"ruleId": "871", "severity": 1, "message": "1068", "line": 28, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 28, "endColumn": 12}, {"ruleId": "871", "severity": 1, "message": "1069", "line": 29, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 29, "endColumn": 19}, {"ruleId": "871", "severity": 1, "message": "1070", "line": 30, "column": 3, "nodeType": "873", "messageId": "874", "endLine": 30, "endColumn": 19}, {"ruleId": "871", "severity": 1, "message": "902", "line": 34, "column": 10, "nodeType": "873", "messageId": "874", "endLine": 34, "endColumn": 17}, {"ruleId": "871", "severity": 1, "message": "1071", "line": 37, "column": 17, "nodeType": "873", "messageId": "874", "endLine": 37, "endColumn": 31}, {"ruleId": "925", "severity": 1, "message": "1072", "line": 98, "column": 6, "nodeType": "927", "endLine": 98, "endColumn": 24, "suggestions": "1073"}, "no-unused-vars", "'CardActions' is defined but never used.", "Identifier", "unusedVar", "react/jsx-pascal-case", "Imported JSX component CertificazioneCEI64_8Page must be in PascalCase or SCREAMING_SNAKE_CASE", "JSXOpeningElement", "usePascalOrSnakeCase", "no-throw-literal", "Expected an error object to be thrown.", "ThrowStatement", "object", "'Avatar' is defined but never used.", "'AdminIcon' is defined but never used.", "'ConstructionIcon' is defined but never used.", "'CableIcon' is defined but never used.", "'ReportIcon' is defined but never used.", "'homeAnchorEl' is assigned a value but never used.", "'adminAnchorEl' is assigned a value but never used.", "'cantieriAnchorEl' is assigned a value but never used.", "'caviAnchorEl' is assigned a value but never used.", "'selectedCantiereName' is assigned a value but never used.", "'React' is defined but never used.", "'Grid' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'CardActionArea' is defined but never used.", "'navigateTo' is assigned a value but never used.", "'Divider' is defined but never used.", "'HomeIcon' is defined but never used.", "'ViewListIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'EditIcon' is defined but never used.", "'DeleteIcon' is defined but never used.", "'HistoryIcon' is defined but never used.", "'ParcoCavi' is defined but never used.", "'isImpersonating' is assigned a value but never used.", "'handleSuccess' is assigned a value but never used.", "'handleError' is assigned a value but never used.", "'lastCheck' is assigned a value but never used.", "'Typography' is defined but never used.", "'Paper' is defined but never used.", "'IconButton' is defined but never used.", "'RefreshIcon' is defined but never used.", "'AdminHomeButton' is defined but never used.", "'cantiereName' is assigned a value but never used.", "'LinearProgress' is defined but never used.", "'InfoIcon' is defined but never used.", "'CavoForm' is defined but never used.", "'navigate' is assigned a value but never used.", "'setFilters' is assigned a value but never used.", "'statiInstallazione' is assigned a value but never used.", "'tipologieCavi' is assigned a value but never used.", "'setTipologieCavi' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'error' and 'user'. Either include them or remove the dependency array.", "ArrayExpression", ["1074"], "'handleOpenDetails' is assigned a value but never used.", "'useEffect' is defined but never used.", "'useAuth' is assigned a value but never used.", "'InventoryIcon' is defined but never used.", "'handleBackToCantieri' is assigned a value but never used.", "'Button' is defined but never used.", "'handleBackToAdmin' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'selectCantiere'. Either include it or remove the dependency array.", ["1075"], "'axios' is defined but never used.", "'API_URL' is assigned a value but never used.", "'filePath' is assigned a value but never used.", "'ListItemIcon' is defined but never used.", "'ListItemButton' is defined but never used.", "'BuildIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadCertificazioni'. Either include it or remove the dependency array.", ["1076"], "'WarningIcon' is defined but never used.", "'isEmpty' is defined but never used.", "'isFirstInsertion' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'handleOptionSelect', 'initialOption', and 'loadBobine'. Either include them or remove the dependency array.", ["1077"], "'renderBobineCards' is assigned a value but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'FormHelperText' is defined but never used.", "'formWarnings' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "IfStatement", "unreachableCode", "React Hook React.useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array.", ["1078"], ["1079"], "'token' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'updateCavoForCompatibility'.", "ObjectExpression", "unexpected", "Duplicate key 'getRevisioneCorrente'.", "Duplicate key 'getCaviInstallati'.", "'Stack' is defined but never used.", "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'Legend' is defined but never used.", "'progressData' is assigned a value but never used.", "'caviData' is assigned a value but never used.", "'metricsData' is assigned a value but never used.", "'CustomTooltip' is assigned a value but never used.", "'renderCustomizedLabel' is assigned a value but never used.", "'LineChart' is defined but never used.", "'XAxis' is defined but never used.", "'YAxis' is defined but never used.", "'CartesianGrid' is defined but never used.", "'Tooltip' is defined but never used.", "'ResponsiveContainer' is defined but never used.", "'ComposedChart' is defined but never used.", "'Line' is defined but never used.", "'Chip' is defined but never used.", "'bobineData' is assigned a value but never used.", "'totaliData' is assigned a value but never used.", "'analisiData' is assigned a value but never used.", "'isCompleto' is assigned a value but never used.", "'isInCorso' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array.", ["1080"], "'DownloadIcon' is defined but never used.", "'CABLE_STATES' is defined but never used.", "'REEL_STATES' is defined but never used.", "'determineCableState' is defined but never used.", "'determineReelState' is defined but never used.", "'canModifyCable' is defined but never used.", "'getReelStateColor' is defined but never used.", "'redirectToVisualizzaCavi' is defined but never used.", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadBobine' and 'loadCavi'. Either include them or remove the dependency array.", ["1081"], "React Hook useEffect has a missing dependency: 'filterCompatibleBobine'. Either include it or remove the dependency array.", ["1082"], "'TextField' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", ["1083"], "'FormControl' is defined but never used.", "'ListItemSecondaryAction' is defined but never used.", "'isCableSpare' is defined but never used.", "'isCableInstalled' is defined but never used.", "'getCableStateColor' is defined but never used.", "'searchResults' is assigned a value but never used.", "'setSearchResults' is assigned a value but never used.", "'showSearchResults' is assigned a value but never used.", "'compatibleBobine' is assigned a value but never used.", ["1084"], "React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array.", ["1085"], "React Hook useEffect has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1086"], "'bobina' is assigned a value but never used.", "'SaveIcon' is defined but never used.", "'incompatibleReel' is assigned a value but never used.", "'setIncompatibleReel' is assigned a value but never used.", ["1087"], "'handleBack' is assigned a value but never used.", "'buildFullBobinaId' is assigned a value but never used.", "'hasSufficientMeters' is assigned a value but never used.", "'getStepContent' is assigned a value but never used.", "'config' is defined but never used.", "'sentData' is assigned a value but never used.", "'FormControlLabel' is defined but never used.", ["1088"], "'result' is assigned a value but never used.", "'Alert' is defined but never used.", "'filteredCantieri' is assigned a value but never used.", "'Box' is defined but never used.", "'CircularProgress' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array.", ["1089"], "React Hook useEffect has a missing dependency: 'filterCavi'. Either include it or remove the dependency array.", ["1090"], "React Hook useEffect has a missing dependency: 'filterCertificazioni'. Either include it or remove the dependency array.", ["1091"], "React Hook useEffect has a missing dependency: 'calculateStatistics'. Either include it or remove the dependency array.", ["1092"], "React Hook useEffect has missing dependencies: 'filterCavi' and 'filterCertificazioni'. Either include them or remove the dependency array.", ["1093"], "React Hook useEffect has missing dependencies: 'loadComande' and 'loadStatistiche'. Either include them or remove the dependency array.", ["1094"], "'AssignmentIcon' is defined but never used.", "React Hook React.useEffect has a missing dependency: 'getInitialAction'. Either include it or remove the dependency array.", ["1095"], "Imported JSX component CertificazioneCEI64_8 must be in PascalCase or SCREAMING_SNAKE_CASE", "'setSelectedCertificazione' is assigned a value but never used.", "'ViewIcon' is defined but never used.", "'Accordion' is defined but never used.", "'AccordionSummary' is defined but never used.", "'AccordionDetails' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadProve'. Either include it or remove the dependency array.", ["1096"], {"desc": "1097", "fix": "1098"}, {"desc": "1099", "fix": "1100"}, {"desc": "1101", "fix": "1102"}, {"desc": "1103", "fix": "1104"}, {"desc": "1105", "fix": "1106"}, {"kind": "1107", "justification": "1108"}, {"desc": "1109", "fix": "1110"}, {"desc": "1111", "fix": "1112"}, {"desc": "1113", "fix": "1114"}, {"desc": "1115", "fix": "1116"}, {"desc": "1115", "fix": "1117"}, {"desc": "1118", "fix": "1119"}, {"desc": "1120", "fix": "1121"}, {"desc": "1122", "fix": "1123"}, {"desc": "1124", "fix": "1125"}, {"desc": "1126", "fix": "1127"}, {"desc": "1128", "fix": "1129"}, {"desc": "1130", "fix": "1131"}, {"desc": "1132", "fix": "1133"}, {"desc": "1134", "fix": "1135"}, {"desc": "1136", "fix": "1137"}, {"desc": "1138", "fix": "1139"}, {"desc": "1140", "fix": "1141"}, "Update the dependencies array to be: [error, filters, user]", {"range": "1142", "text": "1143"}, "Update the dependencies array to be: [cantiereId, selectCantiere]", {"range": "1144", "text": "1145"}, "Update the dependencies array to be: [cantiereId, loadCertificazioni]", {"range": "1146", "text": "1147"}, "Update the dependencies array to be: [handleOptionSelect, initialOption, loadBobine]", {"range": "1148", "text": "1149"}, "Update the dependencies array to be: [initialOption, loadCavi]", {"range": "1150", "text": "1151"}, "directive", "", "Update the dependencies array to be: [certificazione, cantiereId, loadCavi]", {"range": "1152", "text": "1153"}, "Update the dependencies array to be: [cantiereId, loadBobine, loadCavi]", {"range": "1154", "text": "1155"}, "Update the dependencies array to be: [selectedCavo, bobine, filterCompatibleBobine]", {"range": "1156", "text": "1157"}, "Update the dependencies array to be: [cantiereId, loadCavi]", {"range": "1158", "text": "1159"}, {"range": "1160", "text": "1159"}, "Update the dependencies array to be: [loadBobine, selectedCavo]", {"range": "1161", "text": "1162"}, "Update the dependencies array to be: [cavoId, cavi, selectedCavo, onError]", {"range": "1163", "text": "1164"}, "Update the dependencies array to be: [cantiereId, loadBobine]", {"range": "1165", "text": "1166"}, "Update the dependencies array to be: [open, bobina, cantiereId, loadCavi]", {"range": "1167", "text": "1168"}, "Update the dependencies array to be: [cantiereId, loadInitialData]", {"range": "1169", "text": "1170"}, "Update the dependencies array to be: [cavi, searchTerm, filters, sortBy, sortOrder, filterCavi]", {"range": "1171", "text": "1172"}, "Update the dependencies array to be: [certificazioni, searchTerm, filters, sortBy, sortOrder, filterCertificazioni]", {"range": "1173", "text": "1174"}, "Update the dependencies array to be: [calculateStatistics, cavi, certificazioni]", {"range": "1175", "text": "1176"}, "Update the dependencies array to be: [activeTab, cavi, certificazioni, filterCavi, filterCertificazioni]", {"range": "1177", "text": "1178"}, "Update the dependencies array to be: [cantiereId, loadComande, loadStatistiche]", {"range": "1179", "text": "1180"}, "Update the dependencies array to be: [getInitialAction, location.pathname]", {"range": "1181", "text": "1182"}, "Update the dependencies array to be: [certificazioneId, loadProve]", {"range": "1183", "text": "1184"}, [19729, 19738], "[error, filters, user]", [1559, 1571], "[cantiereId, selectCantiere]", [3538, 3550], "[cantiereId, loadCertificazioni]", [4642, 4644], "[handleOptionSelect, initialOption, loadBobine]", [3043, 3058], "[initialOption, loadCavi]", [1578, 1606], "[certificazione, cantiereId, loadCavi]", [2572, 2584], "[cantiereId, loadBobine, loadCavi]", [14450, 14472], "[selectedCavo, bobine, filterCompatibleBobine]", [1014, 1026], "[cantiereId, loadCavi]", [3142, 3154], [3288, 3302], "[load<PERSON><PERSON><PERSON>, selected<PERSON>avo]", [3868, 3896], "[cavoId, cavi, selected<PERSON>av<PERSON>, onError]", [4325, 4337], "[cantiereId, loadBobine]", [1912, 1938], "[open, bobina, cantiereId, loadCavi]", [3588, 3600], "[cantiereId, loadInitialData]", [3685, 3731], "[cavi, searchTerm, filters, sortBy, sortOrder, filterCavi]", [3815, 3871], "[certificazioni, searchTerm, filters, sortBy, sortOrder, filterCertificazioni]", [3977, 3999], "[calculateStatistics, cavi, certificazioni]", [4221, 4254], "[activeTab, cavi, certificazioni, filterCavi, filterCertificazioni]", [1528, 1540], "[cantiereId, loadComande, loadStatistiche]", [4982, 5001], "[getInitialAction, location.pathname]", [2516, 2534], "[certificazioneId, loadProve]"]