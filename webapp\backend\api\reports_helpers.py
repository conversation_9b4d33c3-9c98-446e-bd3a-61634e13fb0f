"""
Funzioni helper per la generazione dei report del sistema CMS.
Adattate dalla CLI per funzionare con PostgreSQL.
Supporta sia la generazione di dati per visualizzazione che la creazione di file PDF/Excel.
"""

import logging
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
import sys
import os
import uuid
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
from modules.database_pg import Database
from backend.utils.pdf_generator import crea_pdf_report, crea_excel_report

def _calcola_data_completamento_lavorativa(data_inizio: datetime.date, giorni_lavorativi_necessari: int, posa_storica: List[Tuple]) -> Optional[datetime.date]:
    """
    Calcola la data di completamento considerando solo i giorni lavorativi effettivi.
    Analizza lo storico per capire quali giorni della settimana si lavora normalmente.
    """
    if giorni_lavorativi_necessari <= 0:
        return data_inizio

    # Analizza lo storico per capire i pattern lavorativi
    giorni_settimana_lavorativi = set()

    if posa_storica:
        for row in posa_storica:
            data_posa = row[0]
            if data_posa:
                # 0=Lunedì, 6=Domenica
                giorno_settimana = data_posa.weekday()
                giorni_settimana_lavorativi.add(giorno_settimana)

    # Se non abbiamo dati storici, assume lun-ven (0-4)
    if not giorni_settimana_lavorativi:
        giorni_settimana_lavorativi = {0, 1, 2, 3, 4}  # Lun-Ven

    # Calcola la data di completamento
    data_corrente = data_inizio + timedelta(days=1)  # Inizia dal giorno successivo
    giorni_contati = 0

    # Limite di sicurezza per evitare loop infiniti
    max_iterazioni = giorni_lavorativi_necessari * 3
    iterazioni = 0

    while giorni_contati < giorni_lavorativi_necessari and iterazioni < max_iterazioni:
        if data_corrente.weekday() in giorni_settimana_lavorativi:
            giorni_contati += 1

        if giorni_contati < giorni_lavorativi_necessari:
            data_corrente += timedelta(days=1)

        iterazioni += 1

    return data_corrente if iterazioni < max_iterazioni else None

async def _generate_progress_report_data(cantiere_id: int) -> Dict[str, Any]:
    """Genera i dati per il report di avanzamento in formato video."""
    try:
        db = Database()

        # Ottieni il nome del cantiere
        cantiere_result = db.fetch_one(
            "SELECT nome FROM cantieri WHERE id_cantiere = %s",
            (cantiere_id,)
        )
        nome_cantiere = cantiere_result[0] if cantiere_result else f"Cantiere {cantiere_id}"

        # Calcola i metri totali, posati e residui
        stats_result = db.execute_query("""
            SELECT
                SUM(CASE WHEN stato_installazione != 'Installato' THEN metri_teorici ELSE 0 END) as metri_da_posare,
                SUM(CASE WHEN stato_installazione = 'Installato' THEN metratura_reale ELSE 0 END) as metri_posati,
                SUM(CASE WHEN stato_installazione != 'Installato' THEN metri_teorici ELSE metratura_reale END) as metri_totali,
                COUNT(*) as totale_cavi,
                SUM(CASE WHEN stato_installazione = 'Installato' THEN 1 ELSE 0 END) as cavi_posati
            FROM cavi
            WHERE id_cantiere = %s AND modificato_manualmente != 3
        """, (cantiere_id,), fetch_one=True)

        metri_totali = float(stats_result[2] or 0)
        metri_posati = float(stats_result[1] or 0)
        metri_da_posare = float(stats_result[0] or 0)
        totale_cavi = int(stats_result[3] or 0)
        cavi_posati = int(stats_result[4] or 0)

        # Calcola percentuale di avanzamento
        percentuale_avanzamento = (metri_posati / metri_totali * 100) if metri_totali > 0 else 0
        percentuale_cavi = (cavi_posati / totale_cavi * 100) if totale_cavi > 0 else 0

        # Calcola medie di posa realistiche - considera tutti i giorni di lavoro effettivo
        oggi = datetime.now().date()

        # Ottieni TUTTI i giorni in cui è stata effettuata posa (non solo ultimi 7 giorni)
        # Usa timestamp::date per raggruppare per giorno (campo disponibile nella tabella)
        posa_storica = db.execute_query("""
            SELECT timestamp::date as data_posa, SUM(metratura_reale) as metri_posati
            FROM cavi
            WHERE id_cantiere = %s AND stato_installazione = 'Installato'
                AND timestamp IS NOT NULL AND metratura_reale > 0
            GROUP BY timestamp::date
            ORDER BY timestamp::date DESC
        """, (cantiere_id,), fetch_all=True)

        # Calcola media giornaliera realistica (solo giorni con posa effettiva)
        media_giornaliera = 0
        giorni_lavorativi_effettivi = 0
        totale_metri_posati_storico = 0

        if posa_storica:
            giorni_lavorativi_effettivi = len(posa_storica)
            totale_metri_posati_storico = sum(float(row[1]) for row in posa_storica)
            media_giornaliera = totale_metri_posati_storico / giorni_lavorativi_effettivi if giorni_lavorativi_effettivi > 0 else 0

        # Calcola metri residui reali
        metri_residui = metri_da_posare  # Questo è già calcolato correttamente sopra

        # Calcola giorni stimati per completamento e data di fine realistica
        giorni_stimati = None
        data_completamento = None

        if media_giornaliera > 0 and metri_residui > 0:
            # Calcola giorni lavorativi necessari
            giorni_lavorativi_necessari = int(metri_residui / media_giornaliera) + (1 if metri_residui % media_giornaliera > 0 else 0)

            # Calcola data di completamento considerando solo giorni lavorativi
            data_completamento_calc = _calcola_data_completamento_lavorativa(oggi, giorni_lavorativi_necessari, posa_storica)

            giorni_stimati = giorni_lavorativi_necessari
            data_completamento = data_completamento_calc.strftime('%d/%m/%Y') if data_completamento_calc else None

        # Prepara dati posa recente (ultimi 10 giorni per visualizzazione)
        posa_recente_display = posa_storica[:10] if posa_storica else []

        return {
            "nome_cantiere": nome_cantiere,
            "metri_totali": round(metri_totali, 2),
            "metri_posati": round(metri_posati, 2),
            "metri_da_posare": round(metri_da_posare, 2),
            "percentuale_avanzamento": round(percentuale_avanzamento, 2),
            "totale_cavi": totale_cavi,
            "cavi_posati": cavi_posati,
            "cavi_rimanenti": totale_cavi - cavi_posati,
            "percentuale_cavi": round(percentuale_cavi, 2),
            "media_giornaliera": round(media_giornaliera, 2),
            "giorni_stimati": giorni_stimati,
            "data_completamento": data_completamento,
            "posa_recente": [
                {
                    "data": row[0].strftime('%d/%m/%Y') if row[0] else '',
                    "metri": round(float(row[1]), 2)
                } for row in posa_recente_display
            ],
            # Aggiungi informazioni aggiuntive per il debug e la trasparenza
            "giorni_lavorativi_effettivi": giorni_lavorativi_effettivi,
            "totale_metri_posati_storico": round(totale_metri_posati_storico, 2)
        }
    except Exception as e:
        logging.error(f"Errore nella generazione dati progress report: {str(e)}")
        raise

async def _generate_boq_report_data(cantiere_id: int) -> Dict[str, Any]:
    """Genera i dati per il Bill of Quantities in formato video."""
    try:
        db = Database()

        cantiere_result = db.execute_query(
            "SELECT nome FROM cantieri WHERE id_cantiere = %s",
            (cantiere_id,), fetch_one=True
        )
        nome_cantiere = cantiere_result[0] if cantiere_result else f"Cantiere {cantiere_id}"

        # Query per ottenere i dati dei cavi raggruppati per tipologia e FORMAZIONE
        cavi_per_tipo = db.execute_query("""
            SELECT
                tipologia, sezione as formazione,
                COUNT(*) as num_cavi,
                SUM(metri_teorici) as metri_teorici_totali,
                SUM(metratura_reale) as metri_reali_posati,
                SUM(CASE WHEN stato_installazione != 'Installato' THEN metri_teorici ELSE 0 END) as metri_da_posare
            FROM cavi
            WHERE id_cantiere = %s AND modificato_manualmente != 3
            GROUP BY tipologia, sezione
            ORDER BY tipologia, sezione
        """, (cantiere_id,), fetch_all=True)

        # Query per ottenere i dati delle bobine disponibili
        bobine_per_tipo = db.execute_query("""
            SELECT
                tipologia, sezione as formazione,
                COUNT(*) as num_bobine,
                SUM(metri_residui) as metri_disponibili
            FROM parco_cavi
            WHERE id_cantiere = %s AND stato_bobina != 'TERMINATA'
            GROUP BY tipologia, sezione
            ORDER BY tipologia, sezione
        """, (cantiere_id,), fetch_all=True)

        # Converti i risultati in formato dict
        cavi_data = []
        if cavi_per_tipo:
            for row in cavi_per_tipo:
                cavi_data.append({
                    "tipologia": row[0],
                    "formazione": row[1],  # Rinominato da sezione a formazione
                    "num_cavi": int(row[2]),
                    "metri_teorici": round(float(row[3] or 0), 2),
                    "metri_reali": round(float(row[4] or 0), 2),
                    "metri_da_posare": round(float(row[5] or 0), 2)
                })

        bobine_data = []
        if bobine_per_tipo:
            for row in bobine_per_tipo:
                bobine_data.append({
                    "tipologia": row[0],
                    "formazione": row[1],  # Rinominato da sezione a formazione
                    "num_bobine": int(row[2]),
                    "metri_disponibili": round(float(row[3] or 0), 2)
                })

        return {
            "nome_cantiere": nome_cantiere,
            "cavi_per_tipo": cavi_data,
            "bobine_per_tipo": bobine_data
        }
    except Exception as e:
        logging.error(f"Errore nella generazione dati BOQ: {str(e)}")
        raise

async def _generate_bobine_report_data(cantiere_id: int) -> Dict[str, Any]:
    """Genera i dati per il report bobine in formato video."""
    try:
        db = Database()

        cantiere_result = db.execute_query(
            "SELECT nome FROM cantieri WHERE id_cantiere = %s",
            (cantiere_id,), fetch_one=True
        )
        nome_cantiere = cantiere_result[0] if cantiere_result else f"Cantiere {cantiere_id}"

        # Query per ottenere tutte le bobine del cantiere
        bobine = db.execute_query("""
            SELECT
                id_bobina,
                tipologia,
                sezione,
                metri_totali,
                metri_residui,
                stato_bobina,
                (metri_totali - metri_residui) as metri_utilizzati,
                CASE
                    WHEN metri_totali > 0 THEN ((metri_totali - metri_residui) / metri_totali * 100)
                    ELSE 0
                END as percentuale_utilizzo
            FROM parco_cavi
            WHERE id_cantiere = %s
            ORDER BY id_bobina
        """, (cantiere_id,), fetch_all=True)

        bobine_data = []
        if bobine:
            for row in bobine:
                bobine_data.append({
                    "id_bobina": row[0],
                    "tipologia": row[1],
                    "sezione": row[2],
                    "metri_totali": round(float(row[3] or 0), 2),
                    "metri_residui": round(float(row[4] or 0), 2),
                    "stato": row[5],
                    "metri_utilizzati": round(float(row[6] or 0), 2),
                    "percentuale_utilizzo": round(float(row[7] or 0), 2)
                })

        return {
            "nome_cantiere": nome_cantiere,
            "bobine": bobine_data,
            "totale_bobine": len(bobine_data)
        }
    except Exception as e:
        logging.error(f"Errore nella generazione dati report bobine: {str(e)}")
        raise

async def _generate_bobina_specifica_report_data(cantiere_id: int, id_bobina: str) -> Dict[str, Any]:
    """Genera i dati per il report bobina specifica."""
    try:
        db = Database()

        # Costruisci l'ID completo della bobina se necessario
        if not id_bobina.startswith(f"C{cantiere_id}_B"):
            id_bobina_completo = f"C{cantiere_id}_B{id_bobina}"
        else:
            id_bobina_completo = id_bobina

        # Query per ottenere i dati della bobina
        bobina = db.execute_query("""
            SELECT
                id_bobina,
                tipologia,
                sezione,
                metri_totali,
                metri_residui,
                stato_bobina
            FROM parco_cavi
            WHERE id_cantiere = %s AND id_bobina = %s
        """, (cantiere_id, id_bobina_completo), fetch_one=True)

        if not bobina:
            raise ValueError(f"Bobina {id_bobina} non trovata")

        # Query per ottenere i cavi associati alla bobina
        cavi_bobina = db.execute_query("""
            SELECT
                id_cavo,
                sistema,
                utility,
                tipologia,
                metri_teorici,
                metratura_reale,
                stato_installazione
            FROM cavi
            WHERE id_cantiere = %s AND id_bobina = %s
            ORDER BY id_cavo
        """, (cantiere_id, id_bobina_completo), fetch_all=True)

        cavi_data = []
        if cavi_bobina:
            for row in cavi_bobina:
                cavi_data.append({
                    "id_cavo": row[0],
                    "sistema": row[1],
                    "utility": row[2],
                    "tipologia": row[3],
                    "metri_teorici": round(float(row[4] or 0), 2),
                    "metri_reali": round(float(row[5] or 0), 2),
                    "stato": row[6]
                })

        metri_utilizzati = float(bobina[3] or 0) - float(bobina[4] or 0)
        percentuale_utilizzo = (metri_utilizzati / float(bobina[3] or 1)) * 100 if bobina[3] else 0

        return {
            "bobina": {
                "id_bobina": bobina[0],
                "tipologia": bobina[1],
                "sezione": bobina[2],
                "metri_totali": round(float(bobina[3] or 0), 2),
                "metri_residui": round(float(bobina[4] or 0), 2),
                "metri_utilizzati": round(metri_utilizzati, 2),
                "percentuale_utilizzo": round(percentuale_utilizzo, 2),
                "stato": bobina[5]
            },
            "cavi_associati": cavi_data,
            "totale_cavi": len(cavi_data)
        }
    except Exception as e:
        logging.error(f"Errore nella generazione dati bobina specifica: {str(e)}")
        raise

async def _generate_posa_periodo_report_data(cantiere_id: int, data_inizio: Optional[str], data_fine: Optional[str]) -> Dict[str, Any]:
    """Genera i dati per il report posa per periodo."""
    try:
        db = Database()

        cantiere_result = db.execute_query(
            "SELECT nome FROM cantieri WHERE id_cantiere = %s",
            (cantiere_id,), fetch_one=True
        )
        nome_cantiere = cantiere_result[0] if cantiere_result else f"Cantiere {cantiere_id}"

        # Se non specificate, usa gli ultimi 30 giorni
        if not data_fine:
            data_fine = datetime.now().date().isoformat()
        if not data_inizio:
            data_inizio = (datetime.now().date() - timedelta(days=30)).isoformat()

        # Query per posa giornaliera nel periodo
        posa_giornaliera = db.execute_query("""
            SELECT timestamp::date as data_posa, SUM(metratura_reale) as metri_posati
            FROM cavi
            WHERE id_cantiere = %s
                AND timestamp::date BETWEEN %s AND %s
                AND stato_installazione = 'Installato'
            GROUP BY timestamp::date
            ORDER BY timestamp::date
        """, (cantiere_id, data_inizio, data_fine), fetch_all=True)

        # Calcola statistiche del periodo
        totale_metri_periodo = sum(float(row[1]) for row in posa_giornaliera) if posa_giornaliera else 0
        giorni_attivi = len(posa_giornaliera) if posa_giornaliera else 0
        media_giornaliera = totale_metri_periodo / giorni_attivi if giorni_attivi > 0 else 0

        posa_data = []
        if posa_giornaliera:
            for row in posa_giornaliera:
                posa_data.append({
                    "data": row[0].strftime('%d/%m/%Y') if row[0] else '',
                    "metri": round(float(row[1]), 2)
                })

        return {
            "nome_cantiere": nome_cantiere,
            "data_inizio": data_inizio,
            "data_fine": data_fine,
            "posa_giornaliera": posa_data,
            "totale_metri_periodo": round(totale_metri_periodo, 2),
            "giorni_attivi": giorni_attivi,
            "media_giornaliera": round(media_giornaliera, 2)
        }
    except Exception as e:
        logging.error(f"Errore nella generazione dati posa periodo: {str(e)}")
        raise

async def _generate_cavi_stato_report_data(cantiere_id: int) -> Dict[str, Any]:
    """Genera i dati per il report cavi per stato."""
    try:
        db = Database()

        cantiere_result = db.execute_query(
            "SELECT nome FROM cantieri WHERE id_cantiere = %s",
            (cantiere_id,), fetch_one=True
        )
        nome_cantiere = cantiere_result[0] if cantiere_result else f"Cantiere {cantiere_id}"

        # Query per ottenere i cavi raggruppati per stato
        cavi_per_stato = db.execute_query("""
            SELECT
                stato_installazione,
                COUNT(*) as num_cavi,
                SUM(metri_teorici) as metri_teorici_totali,
                SUM(metratura_reale) as metri_reali_totali
            FROM cavi
            WHERE id_cantiere = %s AND modificato_manualmente != 3
            GROUP BY stato_installazione
            ORDER BY stato_installazione
        """, (cantiere_id,), fetch_all=True)

        stati_data = []
        if cavi_per_stato:
            for row in cavi_per_stato:
                stati_data.append({
                    "stato": row[0],
                    "num_cavi": int(row[1]),
                    "metri_teorici": round(float(row[2] or 0), 2),
                    "metri_reali": round(float(row[3] or 0), 2)
                })

        return {
            "nome_cantiere": nome_cantiere,
            "cavi_per_stato": stati_data
        }
    except Exception as e:
        logging.error(f"Errore nella generazione dati cavi per stato: {str(e)}")
        raise

# Funzioni per la generazione di file (PDF/Excel) - da implementare successivamente
async def _generate_progress_report_file(cantiere_id: int, formato: str) -> Optional[str]:
    """
    Genera il file del report di avanzamento.

    Args:
        cantiere_id: ID del cantiere
        formato: Formato del report ('pdf' o 'excel')

    Returns:
        str: Percorso del file generato, o None in caso di errore
    """
    try:
        # Ottieni i dati del report
        report_data = await _generate_progress_report_data(cantiere_id)

        # Ottieni il nome del cantiere
        nome_cantiere = report_data.get('nome_cantiere', f'Cantiere {cantiere_id}')

        # Genera un nome file univoco
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        file_id = str(uuid.uuid4())[:8]

        if formato.lower() == 'pdf':
            # Prepara i dati per il PDF
            titolo = f"Report di Avanzamento"
            sottotitolo = f"Cantiere: {nome_cantiere}"

            # Prepara le colonne e i dati per la tabella principale
            colonne = ["Metrica", "Valore"]
            dati = [
                ["Metri totali", f"{report_data.get('metri_totali', 0):,.2f}"],
                ["Metri posati", f"{report_data.get('metri_posati', 0):,.2f}"],
                ["Metri da posare", f"{report_data.get('metri_da_posare', 0):,.2f}"],
                ["Percentuale avanzamento", f"{report_data.get('percentuale_avanzamento', 0):,.2f}%"],
                ["Totale cavi", f"{report_data.get('totale_cavi', 0)}"],
                ["Cavi posati", f"{report_data.get('cavi_posati', 0)}"],
                ["Cavi rimanenti", f"{report_data.get('cavi_rimanenti', 0)}"],
                ["Percentuale cavi", f"{report_data.get('percentuale_cavi', 0):,.2f}%"],
                ["Media giornaliera", f"{report_data.get('media_giornaliera', 0):,.2f} m/giorno"],
                ["Giorni stimati", f"{report_data.get('giorni_stimati', 'N/A')}"],
                ["Data completamento stimata", f"{report_data.get('data_completamento', 'N/A')}"]
            ]

            # Prepara la tabella aggiuntiva per la posa recente
            tabelle_aggiuntive = []
            if report_data.get('posa_recente'):
                posa_recente = report_data.get('posa_recente', [])
                colonne_posa = ["Data", "Metri posati"]
                dati_posa = [[item.get('data', ''), f"{item.get('metri', 0):,.2f}"] for item in posa_recente]

                tabelle_aggiuntive.append({
                    'titolo': "Posa Recente",
                    'colonne': colonne_posa,
                    'dati': dati_posa
                })

            # Genera il PDF
            nome_file = f"progress_report_{cantiere_id}_{timestamp}_{file_id}.pdf"
            return crea_pdf_report(titolo, sottotitolo, dati, colonne, nome_file, tabelle_aggiuntive)

        elif formato.lower() == 'excel':
            # Prepara i dati per l'Excel
            dati_principali = [
                {"Metrica": "Metri totali", "Valore": report_data.get('metri_totali', 0)},
                {"Metrica": "Metri posati", "Valore": report_data.get('metri_posati', 0)},
                {"Metrica": "Metri da posare", "Valore": report_data.get('metri_da_posare', 0)},
                {"Metrica": "Percentuale avanzamento", "Valore": report_data.get('percentuale_avanzamento', 0)},
                {"Metrica": "Totale cavi", "Valore": report_data.get('totale_cavi', 0)},
                {"Metrica": "Cavi posati", "Valore": report_data.get('cavi_posati', 0)},
                {"Metrica": "Cavi rimanenti", "Valore": report_data.get('cavi_rimanenti', 0)},
                {"Metrica": "Percentuale cavi", "Valore": report_data.get('percentuale_cavi', 0)},
                {"Metrica": "Media giornaliera", "Valore": report_data.get('media_giornaliera', 0)},
                {"Metrica": "Giorni stimati", "Valore": report_data.get('giorni_stimati', 'N/A')},
                {"Metrica": "Data completamento stimata", "Valore": report_data.get('data_completamento', 'N/A')}
            ]

            # Prepara i dati aggiuntivi
            fogli_aggiuntivi = {}
            if report_data.get('posa_recente'):
                fogli_aggiuntivi["Posa Recente"] = report_data.get('posa_recente', [])

            # Genera l'Excel
            nome_file = f"progress_report_{cantiere_id}_{timestamp}_{file_id}.xlsx"
            return crea_excel_report("Report Avanzamento", dati_principali, nome_file, fogli_aggiuntivi)

        else:
            logging.error(f"Formato non supportato: {formato}")
            return None

    except Exception as e:
        logging.error(f"Errore nella generazione del file report avanzamento: {str(e)}")
        return None

async def _generate_boq_report_file(cantiere_id: int, formato: str) -> Optional[str]:
    """
    Genera il file del Bill of Quantities.

    Args:
        cantiere_id: ID del cantiere
        formato: Formato del report ('pdf' o 'excel')

    Returns:
        str: Percorso del file generato, o None in caso di errore
    """
    try:
        # Ottieni i dati del report
        report_data = await _generate_boq_report_data(cantiere_id)

        # Ottieni il nome del cantiere
        nome_cantiere = report_data.get('nome_cantiere', f'Cantiere {cantiere_id}')

        # Genera un nome file univoco
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        file_id = str(uuid.uuid4())[:8]

        if formato.lower() == 'pdf':
            # Prepara i dati per il PDF
            titolo = f"Bill of Quantities (BOQ)"
            sottotitolo = f"Cantiere: {nome_cantiere}"

            # Prepara le tabelle
            tabelle_aggiuntive = []

            # Tabella principale: cavi per tipo
            cavi_per_tipo = report_data.get('cavi_per_tipo', [])
            if cavi_per_tipo:
                colonne_cavi = ["Tipologia", "Formazione", "Num. Cavi", "Metri Teorici", "Metri Reali", "Metri da Posare"]
                dati_cavi = [
                    [
                        item.get('tipologia', ''),
                        item.get('formazione', ''),
                        str(item.get('num_cavi', 0)),
                        f"{item.get('metri_teorici', 0):,.2f}",
                        f"{item.get('metri_reali', 0):,.2f}",
                        f"{item.get('metri_da_posare', 0):,.2f}"
                    ]
                    for item in cavi_per_tipo
                ]

                # Usa questa come tabella principale
                colonne = colonne_cavi
                dati = dati_cavi
            else:
                # Tabella vuota se non ci sono dati
                colonne = ["Tipologia", "Formazione", "Num. Cavi", "Metri Teorici", "Metri Reali", "Metri da Posare"]
                dati = [["Nessun dato disponibile", "", "", "", "", ""]]

            # Tabella aggiuntiva: bobine per tipo
            bobine_per_tipo = report_data.get('bobine_per_tipo', [])
            if bobine_per_tipo:
                colonne_bobine = ["Tipologia", "Formazione", "Num. Bobine", "Metri Disponibili"]
                dati_bobine = [
                    [
                        item.get('tipologia', ''),
                        item.get('formazione', ''),
                        str(item.get('num_bobine', 0)),
                        f"{item.get('metri_disponibili', 0):,.2f}"
                    ]
                    for item in bobine_per_tipo
                ]

                tabelle_aggiuntive.append({
                    'titolo': "Bobine Disponibili per Tipo",
                    'colonne': colonne_bobine,
                    'dati': dati_bobine
                })

            # Genera il PDF
            nome_file = f"boq_report_{cantiere_id}_{timestamp}_{file_id}.pdf"
            return crea_pdf_report(titolo, sottotitolo, dati, colonne, nome_file, tabelle_aggiuntive)

        elif formato.lower() == 'excel':
            # Prepara i dati per l'Excel
            fogli_aggiuntivi = {}

            # Foglio principale: cavi per tipo
            cavi_per_tipo = report_data.get('cavi_per_tipo', [])
            if not cavi_per_tipo:
                cavi_per_tipo = [{"tipologia": "Nessun dato disponibile", "formazione": "", "num_cavi": 0, 
                                 "metri_teorici": 0, "metri_reali": 0, "metri_da_posare": 0}]

            # Foglio aggiuntivo: bobine per tipo
            bobine_per_tipo = report_data.get('bobine_per_tipo', [])
            if bobine_per_tipo:
                fogli_aggiuntivi["Bobine Disponibili"] = bobine_per_tipo

            # Genera l'Excel
            nome_file = f"boq_report_{cantiere_id}_{timestamp}_{file_id}.xlsx"
            return crea_excel_report("Cavi per Tipo", cavi_per_tipo, nome_file, fogli_aggiuntivi)

        else:
            logging.error(f"Formato non supportato: {formato}")
            return None

    except Exception as e:
        logging.error(f"Errore nella generazione del file BOQ: {str(e)}")
        return None

async def _generate_bobine_report_file(cantiere_id: int, formato: str) -> Optional[str]:
    """
    Genera il file del report bobine.

    Args:
        cantiere_id: ID del cantiere
        formato: Formato del report ('pdf' o 'excel')

    Returns:
        str: Percorso del file generato, o None in caso di errore
    """
    try:
        # Ottieni i dati del report
        report_data = await _generate_bobine_report_data(cantiere_id)

        # Ottieni il nome del cantiere
        nome_cantiere = report_data.get('nome_cantiere', f'Cantiere {cantiere_id}')

        # Genera un nome file univoco
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        file_id = str(uuid.uuid4())[:8]

        if formato.lower() == 'pdf':
            # Prepara i dati per il PDF
            titolo = f"Report Utilizzo Bobine"
            sottotitolo = f"Cantiere: {nome_cantiere}"

            # Prepara le colonne e i dati per la tabella principale
            bobine = report_data.get('bobine', [])
            if bobine:
                colonne = ["ID Bobina", "Tipologia", "Sezione", "Metri Totali", "Metri Residui", "Metri Utilizzati", "% Utilizzo", "Stato"]
                dati = [
                    [
                        item.get('id_bobina', ''),
                        item.get('tipologia', ''),
                        item.get('sezione', ''),
                        f"{item.get('metri_totali', 0):,.2f}",
                        f"{item.get('metri_residui', 0):,.2f}",
                        f"{item.get('metri_utilizzati', 0):,.2f}",
                        f"{item.get('percentuale_utilizzo', 0):,.2f}%",
                        item.get('stato', '')
                    ]
                    for item in bobine
                ]
            else:
                colonne = ["ID Bobina", "Tipologia", "Sezione", "Metri Totali", "Metri Residui", "Metri Utilizzati", "% Utilizzo", "Stato"]
                dati = [["Nessuna bobina disponibile", "", "", "", "", "", "", ""]]

            # Genera il PDF
            nome_file = f"bobine_report_{cantiere_id}_{timestamp}_{file_id}.pdf"
            return crea_pdf_report(titolo, sottotitolo, dati, colonne, nome_file)

        elif formato.lower() == 'excel':
            # Prepara i dati per l'Excel
            bobine = report_data.get('bobine', [])
            if not bobine:
                bobine = [{"id_bobina": "Nessuna bobina disponibile", "tipologia": "", "sezione": "", 
                          "metri_totali": 0, "metri_residui": 0, "metri_utilizzati": 0, 
                          "percentuale_utilizzo": 0, "stato": ""}]

            # Genera l'Excel
            nome_file = f"bobine_report_{cantiere_id}_{timestamp}_{file_id}.xlsx"
            return crea_excel_report("Bobine", bobine, nome_file)

        else:
            logging.error(f"Formato non supportato: {formato}")
            return None

    except Exception as e:
        logging.error(f"Errore nella generazione del file report bobine: {str(e)}")
        return None

async def _generate_bobina_specifica_report_file(cantiere_id: int, id_bobina: str, formato: str) -> Optional[str]:
    """
    Genera il file del report bobina specifica.

    Args:
        cantiere_id: ID del cantiere
        id_bobina: ID della bobina
        formato: Formato del report ('pdf' o 'excel')

    Returns:
        str: Percorso del file generato, o None in caso di errore
    """
    try:
        # Ottieni i dati del report
        report_data = await _generate_bobina_specifica_report_data(cantiere_id, id_bobina)

        # Genera un nome file univoco
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        file_id = str(uuid.uuid4())[:8]

        # Ottieni i dati della bobina
        bobina = report_data.get('bobina', {})
        id_bobina_clean = bobina.get('id_bobina', id_bobina)

        if formato.lower() == 'pdf':
            # Prepara i dati per il PDF
            titolo = f"Report Dettagliato Bobina"
            sottotitolo = f"Bobina: {id_bobina_clean}"

            # Prepara le tabelle
            tabelle_aggiuntive = []

            # Tabella principale: dettagli bobina
            colonne = ["Attributo", "Valore"]
            dati = [
                ["ID Bobina", bobina.get('id_bobina', '')],
                ["Tipologia", bobina.get('tipologia', '')],
                ["Sezione", bobina.get('sezione', '')],
                ["Metri Totali", f"{bobina.get('metri_totali', 0):,.2f}"],
                ["Metri Residui", f"{bobina.get('metri_residui', 0):,.2f}"],
                ["Metri Utilizzati", f"{bobina.get('metri_utilizzati', 0):,.2f}"],
                ["Percentuale Utilizzo", f"{bobina.get('percentuale_utilizzo', 0):,.2f}%"],
                ["Stato", bobina.get('stato', '')]
            ]

            # Tabella aggiuntiva: cavi associati
            cavi_associati = report_data.get('cavi_associati', [])
            if cavi_associati:
                colonne_cavi = ["ID Cavo", "Sistema", "Utility", "Tipologia", "Metri Teorici", "Metri Reali", "Stato"]
                dati_cavi = [
                    [
                        item.get('id_cavo', ''),
                        item.get('sistema', ''),
                        item.get('utility', ''),
                        item.get('tipologia', ''),
                        f"{item.get('metri_teorici', 0):,.2f}",
                        f"{item.get('metri_reali', 0):,.2f}",
                        item.get('stato', '')
                    ]
                    for item in cavi_associati
                ]

                tabelle_aggiuntive.append({
                    'titolo': "Cavi Associati alla Bobina",
                    'colonne': colonne_cavi,
                    'dati': dati_cavi
                })

            # Genera il PDF
            nome_file = f"bobina_specifica_{cantiere_id}_{id_bobina_clean}_{timestamp}_{file_id}.pdf"
            return crea_pdf_report(titolo, sottotitolo, dati, colonne, nome_file, tabelle_aggiuntive)

        elif formato.lower() == 'excel':
            # Prepara i dati per l'Excel
            fogli_aggiuntivi = {}

            # Foglio principale: dettagli bobina
            dati_bobina = [{
                "Attributo": "ID Bobina", "Valore": bobina.get('id_bobina', '')
            }, {
                "Attributo": "Tipologia", "Valore": bobina.get('tipologia', '')
            }, {
                "Attributo": "Sezione", "Valore": bobina.get('sezione', '')
            }, {
                "Attributo": "Metri Totali", "Valore": bobina.get('metri_totali', 0)
            }, {
                "Attributo": "Metri Residui", "Valore": bobina.get('metri_residui', 0)
            }, {
                "Attributo": "Metri Utilizzati", "Valore": bobina.get('metri_utilizzati', 0)
            }, {
                "Attributo": "Percentuale Utilizzo", "Valore": bobina.get('percentuale_utilizzo', 0)
            }, {
                "Attributo": "Stato", "Valore": bobina.get('stato', '')
            }]

            # Foglio aggiuntivo: cavi associati
            cavi_associati = report_data.get('cavi_associati', [])
            if cavi_associati:
                fogli_aggiuntivi["Cavi Associati"] = cavi_associati

            # Genera l'Excel
            nome_file = f"bobina_specifica_{cantiere_id}_{id_bobina_clean}_{timestamp}_{file_id}.xlsx"
            return crea_excel_report("Dettagli Bobina", dati_bobina, nome_file, fogli_aggiuntivi)

        else:
            logging.error(f"Formato non supportato: {formato}")
            return None

    except Exception as e:
        logging.error(f"Errore nella generazione del file report bobina specifica: {str(e)}")
        return None

async def _generate_posa_periodo_report_file(cantiere_id: int, data_inizio: Optional[str], data_fine: Optional[str], formato: str) -> Optional[str]:
    """
    Genera il file del report posa periodo.

    Args:
        cantiere_id: ID del cantiere
        data_inizio: Data inizio periodo (formato ISO)
        data_fine: Data fine periodo (formato ISO)
        formato: Formato del report ('pdf' o 'excel')

    Returns:
        str: Percorso del file generato, o None in caso di errore
    """
    try:
        # Ottieni i dati del report
        report_data = await _generate_posa_periodo_report_data(cantiere_id, data_inizio, data_fine)

        # Ottieni il nome del cantiere
        nome_cantiere = report_data.get('nome_cantiere', f'Cantiere {cantiere_id}')

        # Ottieni le date formattate
        data_inizio_fmt = datetime.fromisoformat(report_data.get('data_inizio')).strftime('%d/%m/%Y')
        data_fine_fmt = datetime.fromisoformat(report_data.get('data_fine')).strftime('%d/%m/%Y')

        # Genera un nome file univoco
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        file_id = str(uuid.uuid4())[:8]

        if formato.lower() == 'pdf':
            # Prepara i dati per il PDF
            titolo = f"Report Posa per Periodo"
            sottotitolo = f"Cantiere: {nome_cantiere} - Periodo: {data_inizio_fmt} - {data_fine_fmt}"

            # Prepara le tabelle
            tabelle_aggiuntive = []

            # Tabella principale: statistiche del periodo
            colonne = ["Metrica", "Valore"]
            dati = [
                ["Totale metri posati nel periodo", f"{report_data.get('totale_metri_periodo', 0):,.2f}"],
                ["Giorni attivi", f"{report_data.get('giorni_attivi', 0)}"],
                ["Media giornaliera", f"{report_data.get('media_giornaliera', 0):,.2f} m/giorno"]
            ]

            # Tabella aggiuntiva: posa giornaliera
            posa_giornaliera = report_data.get('posa_giornaliera', [])
            if posa_giornaliera:
                colonne_posa = ["Data", "Metri posati"]
                dati_posa = [
                    [
                        item.get('data', ''),
                        f"{item.get('metri', 0):,.2f}"
                    ]
                    for item in posa_giornaliera
                ]

                tabelle_aggiuntive.append({
                    'titolo': "Posa Giornaliera",
                    'colonne': colonne_posa,
                    'dati': dati_posa
                })

            # Genera il PDF
            nome_file = f"posa_periodo_{cantiere_id}_{timestamp}_{file_id}.pdf"
            return crea_pdf_report(titolo, sottotitolo, dati, colonne, nome_file, tabelle_aggiuntive)

        elif formato.lower() == 'excel':
            # Prepara i dati per l'Excel
            dati_principali = [
                {"Metrica": "Totale metri posati nel periodo", "Valore": report_data.get('totale_metri_periodo', 0)},
                {"Metrica": "Giorni attivi", "Valore": report_data.get('giorni_attivi', 0)},
                {"Metrica": "Media giornaliera", "Valore": report_data.get('media_giornaliera', 0)}
            ]

            # Prepara i dati aggiuntivi
            fogli_aggiuntivi = {}
            posa_giornaliera = report_data.get('posa_giornaliera', [])
            if posa_giornaliera:
                fogli_aggiuntivi["Posa Giornaliera"] = posa_giornaliera

            # Genera l'Excel
            nome_file = f"posa_periodo_{cantiere_id}_{timestamp}_{file_id}.xlsx"
            return crea_excel_report("Statistiche Periodo", dati_principali, nome_file, fogli_aggiuntivi)

        else:
            logging.error(f"Formato non supportato: {formato}")
            return None

    except Exception as e:
        logging.error(f"Errore nella generazione del file report posa periodo: {str(e)}")
        return None

async def _generate_cavi_stato_report_file(cantiere_id: int, formato: str) -> Optional[str]:
    """
    Genera il file del report cavi per stato.

    Args:
        cantiere_id: ID del cantiere
        formato: Formato del report ('pdf' o 'excel')

    Returns:
        str: Percorso del file generato, o None in caso di errore
    """
    try:
        # Ottieni i dati del report
        report_data = await _generate_cavi_stato_report_data(cantiere_id)

        # Ottieni il nome del cantiere
        nome_cantiere = report_data.get('nome_cantiere', f'Cantiere {cantiere_id}')

        # Genera un nome file univoco
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        file_id = str(uuid.uuid4())[:8]

        if formato.lower() == 'pdf':
            # Prepara i dati per il PDF
            titolo = f"Report Cavi per Stato"
            sottotitolo = f"Cantiere: {nome_cantiere}"

            # Prepara le colonne e i dati per la tabella principale
            cavi_per_stato = report_data.get('cavi_per_stato', [])
            if cavi_per_stato:
                colonne = ["Stato", "Numero Cavi", "Metri Teorici", "Metri Reali"]
                dati = [
                    [
                        item.get('stato', ''),
                        str(item.get('num_cavi', 0)),
                        f"{item.get('metri_teorici', 0):,.2f}",
                        f"{item.get('metri_reali', 0):,.2f}"
                    ]
                    for item in cavi_per_stato
                ]

                # Aggiungi riga totale
                totale_cavi = sum(item.get('num_cavi', 0) for item in cavi_per_stato)
                totale_metri_teorici = sum(item.get('metri_teorici', 0) for item in cavi_per_stato)
                totale_metri_reali = sum(item.get('metri_reali', 0) for item in cavi_per_stato)

                dati.append([
                    "TOTALE",
                    str(totale_cavi),
                    f"{totale_metri_teorici:,.2f}",
                    f"{totale_metri_reali:,.2f}"
                ])
            else:
                colonne = ["Stato", "Numero Cavi", "Metri Teorici", "Metri Reali"]
                dati = [["Nessun dato disponibile", "", "", ""]]

            # Genera il PDF
            nome_file = f"cavi_stato_report_{cantiere_id}_{timestamp}_{file_id}.pdf"
            return crea_pdf_report(titolo, sottotitolo, dati, colonne, nome_file)

        elif formato.lower() == 'excel':
            # Prepara i dati per l'Excel
            cavi_per_stato = report_data.get('cavi_per_stato', [])
            if not cavi_per_stato:
                cavi_per_stato = [{"stato": "Nessun dato disponibile", "num_cavi": 0, "metri_teorici": 0, "metri_reali": 0}]

            # Aggiungi riga totale
            if len(cavi_per_stato) > 0 and cavi_per_stato[0].get('stato') != "Nessun dato disponibile":
                totale_cavi = sum(item.get('num_cavi', 0) for item in cavi_per_stato)
                totale_metri_teorici = sum(item.get('metri_teorici', 0) for item in cavi_per_stato)
                totale_metri_reali = sum(item.get('metri_reali', 0) for item in cavi_per_stato)

                cavi_per_stato.append({
                    "stato": "TOTALE",
                    "num_cavi": totale_cavi,
                    "metri_teorici": totale_metri_teorici,
                    "metri_reali": totale_metri_reali
                })

            # Genera l'Excel
            nome_file = f"cavi_stato_report_{cantiere_id}_{timestamp}_{file_id}.xlsx"
            return crea_excel_report("Cavi per Stato", cavi_per_stato, nome_file)

        else:
            logging.error(f"Formato non supportato: {formato}")
            return None

    except Exception as e:
        logging.error(f"Errore nella generazione del file report cavi per stato: {str(e)}")
        return None
