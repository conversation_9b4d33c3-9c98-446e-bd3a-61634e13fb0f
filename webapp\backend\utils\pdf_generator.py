"""
Utility per la generazione di report PDF nel backend della webapp.
Adattato dal modulo reports.py del sistema principale.
"""

import os
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple, Union

# Importazioni per la generazione di PDF
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.lib import colors
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import cm
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    logging.warning("ReportLab non è installato. La generazione di PDF non sarà disponibile.")

# Importazioni per Excel
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    logging.warning("Pandas non è installato. La generazione di Excel non sarà disponibile.")

# Directory per i report
PDF_REPORTS_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'static', 'reports')
os.makedirs(PDF_REPORTS_DIR, exist_ok=True)

def crea_pdf_report(titolo: str, sottotitolo: str, dati: List[List], colonne: List[str], 
                   nome_file: str, tabelle_aggiuntive: Optional[List[Dict[str, Any]]] = None) -> Optional[str]:
    """
    Crea un report PDF generico con supporto per tabelle multiple.
    
    Args:
        titolo: Titolo principale del report
        sottotitolo: Sottotitolo del report
        dati: Lista di righe per la tabella principale
        colonne: Lista di intestazioni per la tabella principale
        nome_file: Nome del file PDF da generare
        tabelle_aggiuntive: Lista di dizionari con dati per tabelle aggiuntive
            Ogni dizionario deve contenere:
            - 'titolo': Titolo della tabella
            - 'dati': Lista di righe
            - 'colonne': Lista di intestazioni
    
    Returns:
        str: Percorso del file PDF generato, o None se ReportLab non è disponibile
    """
    # Verifica se ReportLab è disponibile
    if not REPORTLAB_AVAILABLE:
        logging.error("Impossibile generare il PDF: ReportLab non è installato.")
        return None
    
    # Percorso completo del file
    pdf_path = os.path.join(PDF_REPORTS_DIR, nome_file)
    
    # Crea il documento con margini appropriati
    doc = SimpleDocTemplate(pdf_path, pagesize=A4,
                           leftMargin=2 * cm, rightMargin=2 * cm,
                           topMargin=2 * cm, bottomMargin=2 * cm)
    
    # Contenuto del documento
    elements = []
    
    # Definizione stili personalizzati
    styles = getSampleStyleSheet()
    
    # Stile per il titolo principale
    title_style = ParagraphStyle(
        'TitleStyle',
        parent=styles['Heading1'],
        fontSize=16,
        leading=18,
        spaceAfter=12,
        alignment=1  # Centrato
    )
    
    # Stile per il sottotitolo
    subtitle_style = ParagraphStyle(
        'SubtitleStyle',
        parent=styles['Heading2'],
        fontSize=12,
        leading=14,
        spaceAfter=12,
        textColor=colors.darkblue,
        alignment=1  # Centrato
    )
    
    # Stile per le sezioni
    section_style = ParagraphStyle(
        'SectionStyle',
        parent=styles['Heading3'],
        fontSize=11,
        leading=13,
        spaceBefore=12,
        spaceAfter=6,
        textColor=colors.darkblue
    )
    
    # Stile per il testo normale
    normal_style = styles['Normal']
    
    # Aggiungi titolo e sottotitolo
    elements.append(Paragraph(titolo, title_style))
    elements.append(Paragraph(sottotitolo, subtitle_style))
    elements.append(Spacer(1, 0.5 * cm))
    
    # Aggiungi data generazione
    data_gen = f"<i>Report generato il {datetime.now().strftime('%d/%m/%Y alle %H:%M:%S')}</i>"
    elements.append(Paragraph(data_gen, normal_style))
    elements.append(Spacer(1, 1 * cm))
    
    # Funzione per creare una tabella con stile
    def crea_tabella_con_stile(dati_tabella, colonne_tabella, titolo_tabella=None):
        # Aggiungi titolo della sezione se presente
        if titolo_tabella:
            elements.append(Paragraph(titolo_tabella, section_style))
            elements.append(Spacer(1, 0.3 * cm))
        
        # Prepara i dati per la tabella
        table_data = [colonne_tabella]  # Prima riga: intestazioni
        table_data.extend(dati_tabella)  # Aggiungi i dati
        
        # Determina la larghezza delle colonne in base al contenuto
        num_cols = len(colonne_tabella)
        col_widths = ['*'] * num_cols  # Distribuisce uniformemente lo spazio
        
        # Per colonne con valori numerici, riduci la larghezza
        for i, col in enumerate(colonne_tabella):
            if any('.' in str(row[i]) for row in dati_tabella if i < len(row)):
                col_widths[i] = 1.5 * cm
        
        # Crea la tabella
        table = Table(table_data,
                     colWidths=col_widths,
                     repeatRows=1,
                     hAlign='LEFT')
        
        # Stile della tabella
        table_style = TableStyle([
            # Intestazione
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#4472C4')),  # Blu più scuro
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
            
            # Righe dati
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
            ('ALIGN', (0, 1), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('BOTTOMPADDING', (0, 1), (-1, -1), 6),
            
            # Allineamento a destra per colonne numeriche
            ('ALIGN', (1, 1), (-1, -1), 'RIGHT'),
            
            # Bordi
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('BOX', (0, 0), (-1, -1), 1, colors.black),
            ('LINEABOVE', (0, 1), (-1, 1), 1, colors.black),
            
            # Righe alternate
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor('#E6F0FF')])
        ])
        
        table.setStyle(table_style)
        elements.append(table)
        elements.append(Spacer(1, 0.5 * cm))
    
    # Crea la tabella principale
    crea_tabella_con_stile(dati, colonne, "Dati Principali")
    
    # Crea tabelle aggiuntive se presenti
    if tabelle_aggiuntive:
        for tabella in tabelle_aggiuntive:
            crea_tabella_con_stile(
                tabella.get('dati', []),
                tabella.get('colonne', []),
                tabella.get('titolo', 'Tabella Aggiuntiva')
            )
    
    # Genera il PDF
    doc.build(elements)
    
    logging.info(f"Report PDF generato: {pdf_path}")
    return pdf_path

def crea_excel_report(titolo: str, dati: List[Dict[str, Any]], nome_file: str, 
                     fogli_aggiuntivi: Optional[Dict[str, List[Dict[str, Any]]]] = None) -> Optional[str]:
    """
    Crea un report Excel con supporto per fogli multipli.
    
    Args:
        titolo: Nome del foglio principale
        dati: Lista di dizionari con i dati per il foglio principale
        nome_file: Nome del file Excel da generare
        fogli_aggiuntivi: Dizionario con nomi dei fogli e liste di dizionari di dati
    
    Returns:
        str: Percorso del file Excel generato, o None se Pandas non è disponibile
    """
    # Verifica se Pandas è disponibile
    if not PANDAS_AVAILABLE:
        logging.error("Impossibile generare l'Excel: Pandas non è installato.")
        return None
    
    # Percorso completo del file
    excel_path = os.path.join(PDF_REPORTS_DIR, nome_file)
    
    # Crea un Excel writer
    with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
        # Crea il foglio principale
        df_principale = pd.DataFrame(dati)
        df_principale.to_excel(writer, sheet_name=titolo, index=False)
        
        # Crea fogli aggiuntivi se presenti
        if fogli_aggiuntivi:
            for nome_foglio, dati_foglio in fogli_aggiuntivi.items():
                df_foglio = pd.DataFrame(dati_foglio)
                df_foglio.to_excel(writer, sheet_name=nome_foglio, index=False)
    
    logging.info(f"Report Excel generato: {excel_path}")
    return excel_path